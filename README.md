# MyLLM Proxy Server

MyLLM Proxy Server is a Node.js application designed to act as an intermediary between your AI agents/clients and the SuperWhisper API. It provides OpenAI-compatible and Anthropic-compatible endpoints, handles request/response transformations, manages authentication, and standardizes interactions with different SuperWhisper model backends.

## Quickstart Bash Commands

Start Server: npm start

Start Ngrok: ngrok start myllm

Tree: tree -I "fetch-files|logs|node_modules|.claude" -P "*" --prune -I "README.md|proxy-server-prd.md|proxy.log"

## Google AI Studio Export

mkdir -p /Users/<USER>/Desktop/myllm-server-files && cp cli.js package-lock.json package.json server.js /Users/<USER>/Desktop/myllm-server-files/ && find src -name "*.js" -type f -exec cp {} /Users/<USER>/Desktop/myllm-server-files/ \;

## Features

*   **OpenAI & Anthropic Compatibility:** Exposes `/chat/completions` and `/models` endpoints mimicking OpenAI and Anthropic APIs.
*   **SuperWhisper Integration:** Routes requests to the appropriate SuperWhisper API (OpenAI-style or Anthropic-style) based on model mapping.
*   **Request Transformation:**
    *   Automatically sets `stream: true` for all SuperWhisper requests.
    *   Transforms request payloads from `claude-cli` user agents to a compatible format.
*   **Response Formatting:** Accumulates streamed responses from SuperWhisper and formats them into a single JSON response matching the client's expected API style (OpenAI or Anthropic).
*   **Authentication:** Secures the `/chat/completions` endpoint using a bearer token (`PROXY_AUTH_TOKEN`).
*   **Model Listing:** Provides a static list of supported client-facing models via the `/models` endpoint.
*   **Configuration via Environment Variables:** Flexible setup using a `.env` file.
*   **Detailed Logging:** Logs request/response cycles, errors, and important events to `proxy.log`.
*   **Dry Run Mode:** Allows testing request preparation and logging without actually calling the SuperWhisper API.
*   **CORS Enabled:** Configurable Cross-Origin Resource Sharing.

## Prerequisites

*   [Node.js](https://nodejs.org/) (v14.x or higher recommended)
*   [npm](https://www.npmjs.com/) (usually comes with Node.js)

## Environment Variables

Create a `.env` file in the root of the project directory and populate it with the following variables. See `.env.example` if one is provided (you might want to create one).

| Variable                              | Description                                                                                                | Example                                   | Required |
| ------------------------------------- | ---------------------------------------------------------------------------------------------------------- | ----------------------------------------- | -------- |
| `PORT`                                | Port number for the proxy server to listen on.                                                             | `4000`                                    | No (Defaults to 4000) |
| `PROXY_AUTH_TOKEN`                    | Bearer token required for clients to authenticate with the `/chat/completions` endpoint.                   | `your-secret-proxy-token`                 | **Yes** (for protected endpoint) |
| `SUPERWHISPER_API_BASE_URL_OPENAI`    | Base URL for the SuperWhisper API that emulates OpenAI.                                                    | `https://api.superwhisper.com/openai/v1`  | **Yes**  |
| `SUPERWHISPER_API_BASE_URL_ANTHROPIC` | Base URL for the SuperWhisper API that emulates Anthropic.                                                 | `https://api.superwhisper.com/anthropic/v1` | **Yes**  |
| `SUPERWHISPER_USER_ID`                | User ID to be included in requests to the SuperWhisper API.                                                | `user-12345`                              | **Yes**  |
| `SUPERWHISPER_HEADER_X_ID`            | Value for the `x-id` header sent to SuperWhisper.                                                          | `your-superwhisper-x-id`                  | **Yes**  |
| `SUPERWHISPER_HEADER_X_LICENSE`       | Value for the `x-license` header sent to SuperWhisper.                                                     | `your-superwhisper-license-key`           | **Yes**  |
| `SUPERWHISPER_HEADER_X_SIGNATURE`     | Value for the `x-signature` header sent to SuperWhisper.                                                   | `your-superwhisper-signature`             | **Yes**  |
| `SUPERWHISPER_REQUEST_USER_AGENT`     | Custom User-Agent string for requests made to SuperWhisper.                                                | `MyCustomClient/1.0`                      | No (Defaults to a SuperWhisper-like UA) |
| `LOG_LEVEL`                           | Sets the logging level. Options: `DEBUG`, `INFO`, `WARN`, `ERROR`.                                         | `INFO`                                    | No (Defaults to `INFO`) |
| `DRY_RUN`                             | If set to `true`, API calls to SuperWhisper are logged but not executed. Returns a mock success response.  | `false`                                   | No (Defaults to `false`) |

**Note:** If `PROXY_AUTH_TOKEN` is not set, the `/chat/completions` endpoint will be unprotected, and a warning will be logged at startup. For production, ensure this is set.

## Installation

1.  **Clone the repository:**
    ```bash
    git clone <repository-url>
    cd myllm-proxy
    ```

2.  **Install dependencies:**
    ```bash
    npm install
    ```

3.  **Create and configure your `.env` file** as described in the "Environment Variables" section.

## Running the Server

*   **Start the server (production mode):**
    Uses the `PORT` from `.env` or defaults to `4000`.
    ```bash
    npm start
    ```

*   **Start the server (development mode):**
    Uses port `4001` by default (or as specified in `package.json`'s `dev` script).
    ```bash
    npm run dev
    ```

*   **Run on a custom port:**
    You can override the port using the `-p` or `--port` option:
    ```bash
    node cli.js -p 5000
    # or
    npm start -- -p 5000
    ```

### Dry Run Mode

To enable dry run mode (log requests to SuperWhisper but don't send them), set the environment variable:
`DRY_RUN=true`

Then start the server as usual. Requests to `/chat/completions` will be logged and will return a `{"status": "dry_run_success", ...}` message.

## API Endpoints

### 1. List Models

*   **Endpoint:** `GET /models`
*   **Description:** Returns a list of available client-facing models that this proxy supports. This list is static and defined within the server.
*   **Authentication:** None required.
*   **Example Response:**
    ```json
    {
      "object": "list",
      "data": [
        {
          "id": "claude-3-5-sonnet-20241022",
          "object": "model",
          "created": 1700000000,
          "owned_by": "system"
        },
        {
          "id": "claude-3-7-sonnet-20250219",
          "object": "model",
          "created": 1700000001,
          "owned_by": "system"
        },
        {
          "id": "gpt-4.1-2025-04-14",
          "object": "model",
          "created": 1700000002,
          "owned_by": "system"
        }
      ]
    }
    ```

### 2. Chat Completions

*   **Endpoint:** `POST /chat/completions`
*   **Description:** Proxies chat completion requests to the SuperWhisper API. It determines the target SuperWhisper endpoint (OpenAI-style or Anthropic-style) based on the requested `model`.
*   **Authentication:** Requires a Bearer token. The token must match the `PROXY_AUTH_TOKEN` environment variable.
    *   Header: `Authorization: Bearer <your-proxy-auth-token>`
*   **Request Body (OpenAI-style example):**
    ```json
    {
      "model": "gpt-4.1-2025-04-14",
      "messages": [
        { "role": "user", "content": "Hello, world!" }
      ]
      // Other OpenAI compatible parameters like temperature, max_tokens etc.
      // "stream" parameter from client is ignored; proxy always requests stream from SuperWhisper.
    }
    ```
*   **Request Body (Anthropic-style example):**
    ```json
    {
      "model": "claude-3-5-sonnet-20241022",
      "messages": [
        { "role": "user", "content": "Hello, Claude!" }
      ],
      "max_tokens": 1024
      // Other Anthropic compatible parameters
      // "stream" parameter from client is ignored.
    }
    ```
*   **Response Body:** The response format (OpenAI-style or Anthropic-style) matches the API style associated with the requested model. The proxy accumulates the streamed response from SuperWhisper and returns a single, complete JSON object.
    *   **OpenAI-style Response Example:**
        ```json
        {
          "id": "chatcmpl-proxy_1678886400000",
          "object": "chat.completion",
          "created": 1678886400,
          "model": "gpt-4.1-2025-04-14", // Actual model name used
          "choices": [
            {
              "index": 0,
              "message": {
                "role": "assistant",
                "content": "Response from the model."
              },
              "finish_reason": "stop"
            }
          ]
          // "usage" field is omitted or contains zero/null token counts.
        }
        ```
    *   **Anthropic-style Response Example:**
        ```json
        {
          "id": "msg_proxy_1678886400000",
          "type": "message",
          "role": "assistant",
          "model": "claude-3-5-sonnet-20241022", // Actual model name used
          "content": [
            {
              "type": "text",
              "text": "Response from the model."
            }
          ],
          "stop_reason": "end_turn",
          "stop_sequence": null,
          "usage": {
            "input_tokens": 10, // Example values
            "output_tokens": 25  // Example values
          }
        }
        ```

## Model Mapping

The proxy maintains an internal mapping from client-facing model names to SuperWhisper specific model names:

*   `claude-3-5-sonnet-20241022` -> `sw-claude-3-5-sonnet` (Anthropic-style endpoint)
*   `claude-3-7-sonnet-20250219` -> `sw-claude-3-7-sonnet` (Anthropic-style endpoint)
*   `gpt-4.1-2025-04-14` -> `sw-gpt-4.1` (OpenAI-style endpoint)

The response from `/chat/completions` will generally include the *client-facing model name* in the `model` field for consistency, even if the internal SuperWhisper model name is different (e.g., prefixed with `sw-`).

## User-Agent Specific Transformations

If a request to `/chat/completions` has a `User-Agent` header containing "claude-cli", the proxy will attempt to transform the `messages[].content` field. If `content` is an array of objects (e.g., `[{ "type": "text", "text": "..." }, ...]`), it will be concatenated into a single string.

## Logging

*   Logs are written to `proxy.log` in the project root.
*   Log level can be configured using the `LOG_LEVEL` environment variable (`DEBUG`, `INFO`, `WARN`, `ERROR`). Default is `INFO`.
*   Logs include timestamps, levels, messages, and detailed JSON objects for requests, responses, and errors.
*   Sensitive information in headers (like `Authorization`, `x-license`, `x-signature`, `x-id`) is redacted in the logs.

## Testing

The project includes unit and basic integration tests using Jest.

1.  **Install dev dependencies (if not already done):**
    ```bash
    npm install --only=dev 
    # or if you ran `npm install` initially, they should be there.
    ```
2.  **Run tests:**
    ```bash
    npm test
    ```
    (Currently, the `test` script in `package.json` is `echo "Error: no test specified" && exit 1`. You'll need to update this to `jest` or `npx jest` once Jest is configured.)

    **To run Jest tests (assuming Jest is installed globally or as a dev dependency):**
    ```bash
    npx jest
    # or, if jest is in your package.json scripts:
    # npm test 
    ```
    *Ensure your `package.json`'s `scripts.test` is updated to `jest` or your preferred Jest command.*

## Contributing

Contributions are welcome! Please feel free to submit pull requests or open issues for bugs, feature requests, or improvements.

1.  Fork the repository.
2.  Create your feature branch (`git checkout -b feature/AmazingFeature`).
3.  Commit your changes (`git commit -m 'Add some AmazingFeature'`).
4.  Push to the branch (`git push origin feature/AmazingFeature`).
5.  Open a Pull Request.

## License

This project is licensed under the ISC License - see the `LICENSE` file (or `package.json`) for details.
