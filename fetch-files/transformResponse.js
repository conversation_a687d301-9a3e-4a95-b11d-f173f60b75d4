/**
 * Transforms the content of an Anthropic API response.
 * If a text content block contains a <tool_use> XML block,
 * it attempts to parse this XML into a structured "tool_use" object.
 * The transformation process is as follows:
 * 1. Text strictly before the <tool_use> tag (if any, after trimming) is preserved as a separate text block.
 * 2. The <tool_use>...</tool_use> block itself is parsed.
 *    - If successful, a new content block of type "tool_use" is created:
 *      {
 *        type: "tool_use",
 *        id: "toolu_...", // Auto-generated, e.g., toolu_018QMTL1LKmeSQ8JquDrHYdu
 *        name: "extracted_tool_name",
 *        input: { // Key-value pairs from <tool_parameter> tags
 *          "param_name_1": "param_value_1",
 *          "param_name_2": "param_value_2"
 *        }
 *      }
 *    - The `id` is a string starting with "toolu_" followed by 26 random alphanumeric characters.
 *    - If parsing fails (e.g., the <tool_name> tag is missing or empty), the original
 *      <tool_use>...</tool_use> XML string is preserved as a text block.
 * 3. Text strictly after the </tool_use> tag (if any, after trimming) is preserved as a separate text block.
 *
 * This function is designed to convert a response format similar to 'response-7.json'
 * into a format like 'response-7a.json', where tool invocations are represented as
 * structured objects rather than embedded XML in text.
 *
 * @param {object} responseJson The original response JSON object from the Anthropic API.
 * @returns {object} The transformed response JSON object.
 */
function transformAnthropicResponseContent(responseJson) {
  if (!responseJson || !responseJson.content || !Array.isArray(responseJson.content)) {
    // console.warn('transformAnthropicResponseContent: Invalid input or no content to transform.');
    return responseJson;
  }
  const newContent = [];
  for (const block of responseJson.content) {
    if (block.type === 'text' && typeof block.text === 'string' &&
        block.text.includes('<tool_use>') && block.text.includes('</tool_use>')) {

      const mainPattern = /^([\s\S]*?)(<tool_use>[\s\S]*?<\/tool_use>)([\s\S]*)$/;
      const match = block.text.match(mainPattern);

      if (match) {
        const textBefore = match[1].trim();
        const fullToolUseXML = match[2]; // This is the full <tool_use>...</tool_use> string
        const textAfter = match[3].trim();

        if (textBefore) {
          newContent.push({ type: 'text', text: textBefore });
        }

        // Attempt to parse the fullToolUseXML
        const toolUseContentPattern = /<tool_use>([\s\S]*?)<\/tool_use>/;
        const toolUseContentMatch = fullToolUseXML.match(toolUseContentPattern);

        // toolUseContentMatch[1] contains the content inside <tool_use> tags
        const innerXMLContent = toolUseContentMatch ? toolUseContentMatch[1] : null;

        const toolNamePattern = /<tool_name>([\s\S]*?)<\/tool_name>/;
        const toolNameMatch = innerXMLContent ? innerXMLContent.match(toolNamePattern) : null;

        if (toolNameMatch && toolNameMatch[1] && toolNameMatch[1].trim()) {
          const toolName = toolNameMatch[1].trim();
          const input = {};
          const toolParameterPattern = /<tool_parameter name="([^"]+)">([\s\S]*?)<\/tool_parameter>/g;
          let paramMatch;
          
          if (innerXMLContent) {
            while ((paramMatch = toolParameterPattern.exec(innerXMLContent)) !== null) {
              const paramName = paramMatch[1];
              // Do not trim paramValue to preserve formatting, e.g., for HTML content
              const paramValue = paramMatch[2]; 
              input[paramName] = paramValue;
            }
          }

          // Generate ID: toolu_ + 26 random alphanumeric characters
          const S = "abcdefghijklmnopqrstuvwxyz0123456789";
          const idSuffix = Array.from({ length: 26 }, () => S[Math.floor(Math.random() * S.length)]).join('');
          const generatedId = 'toolu_' + idSuffix;

          newContent.push({
            type: 'tool_use',
            id: generatedId,
            name: toolName,
            input: input
          });
        } else {
          // Parsing failed (e.g., no tool_name), preserve the original XML block as text
          // console.warn('transformAnthropicResponseContent: Malformed <tool_use> block - <tool_name> not found or empty. Preserving as text.');
          newContent.push({ type: 'text', text: fullToolUseXML });
        }

        if (textAfter) {
          newContent.push({ type: 'text', text: textAfter });
        }
      } else {
        // Fallback for structures where <tool_use> tags are present but not matched by mainPattern
        // (e.g., malformed nesting, multiple disjointed blocks which this function isn't designed to handle per block).
        // console.warn('transformAnthropicResponseContent: <tool_use> tags present but main structure not matched. Preserving original block.');
        newContent.push(block);
      }
    } else {
      newContent.push(block);
    }
  }
  return { ...responseJson, content: newContent };
}

module.exports = { transformAnthropicResponseContent };