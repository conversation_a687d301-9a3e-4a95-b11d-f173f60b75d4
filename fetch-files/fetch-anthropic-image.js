const fetch = require('node-fetch');
const Buffer = require('buffer').Buffer;

async function makeApiRequest() {
  const url = 'https://api.superwhisper.com/anthropic/v1/messages';

  const headers = {
    'content-type': 'application/json',
    'x-license': '8C5F4F57-39FA-49E6-A89A-5E3DAC57FE87',
    'accept': '*/*',
    'x-signature': 'fda93c0f6655ff7acb540145f92cbc197f8ed7916b41e28d9a8c2d795c6f77cb',
    'x-id': '6945E2DF-1E4A-51CA-9DB4-1971621C25A1',
    'accept-language': 'en-NZ;q=1.0',
    'accept-encoding': 'br;q=1.0, gzip;q=0.9, deflate;q=0.8',
    'user-agent': 'superwhisper/1.45.14 (com.superduper.superwhisper; build:1.45.14; macOS 15.2.0) Alamofire/5.8.0'
  };

  // Read and encode the local image file
  const fs = require('fs');
  const path = require('path');
  const image_path = path.join(__dirname, 'Screenshot.png');
  const image_media_type = "image/png";
  const image_data = fs.readFileSync(image_path).toString('base64');

  const body = {
    model: "sw-claude-3-5-haiku",
    max_tokens: 8192,
    temperature: 1,
    system: [
      {
        text: "You are a helpful assistant.",
        type: "text",
        cache_control: { type: "ephemeral" }
      }
    ],
    messages: [
      { 
        role: "user", 
        content: [
          {
            type: "image",
            source: {
              type: "base64",
              media_type: image_media_type,
              data: image_data,
            },
          },
          {
            type: "text",
            text: "What is in the above image?"
          }
        ]
      },
    ],
    stream: true
  };

  try {
    const response = await fetch(url, {
      method: 'POST',
      headers: headers,
      body: JSON.stringify(body)
    });

    console.log('Status Code:', response.status);

    const responseHeaders = {};
    response.headers.forEach((value, name) => {
      responseHeaders[name] = value;
    });
    console.log('Response Headers:', responseHeaders);
    const responseBody = await response.text();
    console.log('Response Body:', responseBody);

    // Node.js v20+ supports for await (const chunk of response.body)
    // But if you get "Unexpected reserved word" error, your Node.js is too old.
    // To support older Node.js, use .on('data') and .on('end') streams API.
    if (body.stream) {
      let buffer = '';
      let streamEnded = false;

      response.body.on('data', (chunk) => {
        buffer += chunk.toString();
        let lines = buffer.split('\n');
        buffer = lines.pop(); // Save incomplete line for next chunk

        for (const line of lines) {
          const trimmed = line.trim();
          if (!trimmed) continue;
          // Anthropic streaming: look for event/data lines
          if (trimmed === 'data: [DONE]' || trimmed === 'event: message_stop') {
            streamEnded = true;
            break;
          }
          // Ignore event: ping and other non-data lines
          if (trimmed.startsWith('event:')) {
            continue;
          }
          if (trimmed.startsWith('data: ')) {
            const jsonStr = trimmed.slice(6);
            try {
              const data = JSON.parse(jsonStr);
              // For Claude v3 streaming, the text is in content_block_delta events
              if (data.type === "content_block_delta" && data.delta && typeof data.delta.text === "string") {
                process.stdout.write(data.delta.text);
              }
            } catch (e) {
              console.error('Failed to parse JSON chunk:', e, jsonStr);
            }
          }
        }
        if (streamEnded) {
          response.body.destroy();
        }
      });

      response.body.on('end', () => {
        // If anything left in buffer after stream ends, try to process it
        const last = buffer.trim();
        if (last && last.startsWith('data: ')) {
          const jsonStr = last.slice(6);
          try {
            const data = JSON.parse(jsonStr);
            if (data.type === "content_block_delta" && data.delta && typeof data.delta.text === "string") {
              process.stdout.write(data.delta.text);
            }
          } catch (e) {
            // ignore
          }
        }
        process.exit(0);
      });

      response.body.on('error', (err) => {
        console.error('Stream error:', err);
        process.exit(1);
      });
    }

  } catch (error) {
    console.error('Error making request:', error);
    process.exit(1);
  }
}

makeApiRequest();