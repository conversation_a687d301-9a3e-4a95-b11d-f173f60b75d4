const fetch = require('node-fetch');

async function makeApiRequest() {
  const url = 'https://api.superwhisper.com/v1/chat/completions';

  const headers = {
    'content-type': 'application/json',
    'x-license': '8C5F4F57-39FA-49E6-A89A-5E3DAC57FE87',
    'accept': '*/*',
    'x-signature': 'fda93c0f6655ff7acb540145f92cbc197f8ed7916b41e28d9a8c2d795c6f77cb',
    'x-id': '6945E2DF-1E4A-51CA-9DB4-1971621C25A1',
    'accept-language': 'en-NZ;q=1.0',
    'accept-encoding': 'br;q=1.0, gzip;q=0.9, deflate;q=0.8',
    'user-agent': 'superwhisper/1.45.14 (com.superduper.superwhisper; build:1.45.14; macOS 15.2.0) Alamofire/5.8.0'
  };

  const body = {
    "model": "sw-gpt-4.1",
    "messages": [
      { "role": "developer", "content": "" },
      { "role": "user", "content": "Describe a lion in 1 sentence?" },
      { "role": "assistant", "content": "A lion is a large, powerful carnivorous feline with a tawny coat and a distinctive mane, known as the \"king of the jungle.\"" },
      { "role": "user", "content": "What is it's main prey?" }
    ],
    "stream": true,
    "stream_options": { "include_usage": true }
  };

  try {
    const response = await fetch(url, {
      method: 'POST',
      headers: headers,
      body: JSON.stringify(body)
    });

    console.log('Status Code:', response.status);
    // console.log('Status Text:', response.statusText);

    const responseHeaders = {};
    response.headers.forEach((value, name) => {
      responseHeaders[name] = value;
    });
    console.log('Response Headers:', responseHeaders);

    if (body.stream) {
      // Process the stream without consolidating content
      for await (const chunk of response.body) {
        const rawData = chunk.toString();
        console.log('Raw chunk:', rawData);
      }
      process.exit(0);
    } else {
      // For non-streaming responses
      const responseBody = await response.text();
      console.log('Response Body:', responseBody);
      process.exit(0);
    }

  } catch (error) {
    console.error('Error making request:', error);
    process.exit(1);
  }
}

makeApiRequest();