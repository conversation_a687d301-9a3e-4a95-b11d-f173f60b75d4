## Product Requirements Document: MyLLM Proxy Server
**Version:** 1.0

**1. Introduction**

*   **1.1. Project Name:** MyLLM Proxy Server
*   **1.2. Purpose:** This document outlines the requirements for **developing a new** Node.js proxy server. The server will act as a streamlined, maintainable, and clearly defined intermediary between the user's AI coding agents and the SuperWhisper API. Its primary functions are to translate model names, adapt request/response formats, manage API credentials securely, and provide a standardized interface for specific AI models, ultimately sending non-streamed, complete responses to the AI agents.
*   **1.3. Context:** This project aims to **develop a dedicated proxy server** to streamline interactions with the SuperWhisper API. The focus will be on building a new, robust, and maintainable solution from the outset, establishing a solid foundation for potential future enhancements.

**2. Goals**

*   **2.1. Design for Simplicity:** Ensure the codebase is straightforward, well-structured, and highly maintainable from its inception.
*   **2.2. Reliability:** Provide a stable and predictable proxy service for the specified AI models.
*   **2.3. Standardization:** Ensure responses to AI agents precisely mimic the standard non-streaming API response formats of native OpenAI and Anthropic services.
*   **2.4. Centralization:** Consolidate all SuperWhisper API interactions, including authentication headers and model name translations, within the proxy.
*   **2.5. Debuggability:** Implement comprehensive server-side verbose logging for effective troubleshooting.
*   **2.6. Configurability:** Make all sensitive and environment-specific parameters (API keys, tokens, fixed headers, User IDs) configurable via environment variables.

**3. Supported Models & Endpoints**

*   **3.1. Client-Facing Model Names (Requested by AI Agent):**
    *   `claude-3-5-sonnet-20241022`
    *   `claude-3-7-sonnet-20250219`
    *   `gpt-4.1-2025-04-14`
*   **3.2. SuperWhisper Model Name Mapping (Proxy to SuperWhisper API):**
    *   Client's `claude-3-5-sonnet-20241022` -> Proxy sends `sw-claude-3-5-sonnet` to SuperWhisper.
    *   Client's `claude-3-7-sonnet-20250219` -> Proxy sends `sw-claude-3-7-sonnet` to SuperWhisper.
    *   Client's `gpt-4.1-2025-04-14` -> Proxy sends `sw-gpt-4.1` to SuperWhisper.
*   **3.3. Target SuperWhisper API Endpoints:**
    *   Requests for `sw-claude-3-5-sonnet` and `sw-claude-3-7-sonnet` will be routed to the SuperWhisper Anthropic-style endpoint (e.g., `https://api.superwhisper.com/anthropic/v1/messages`).
    *   Requests for `sw-gpt-4.1` will be routed to the SuperWhisper OpenAI-style endpoint (e.g., `https://api.superwhisper.com/v1/chat/completions`).

**4. Core Functional Requirements**

*   **4.1. `/chat/completions` Endpoint (HTTP POST)**
    *   **4.1.1. Authorization:**
        *   The endpoint must be protected. It shall expect an `Authorization: Bearer <TOKEN>` header in incoming requests.
        *   The received token will be validated against a `PROXY_AUTH_TOKEN` environment variable.
        *   If the token is missing or invalid, the server shall respond with an HTTP 401 Unauthorized status.
    *   **4.1.2. Client Request Handling (Default):**
        *   Accepts JSON payloads from AI coding agents. These payloads are expected to conform to the standard request structures of either OpenAI or Anthropic chat completion APIs.
        *   The proxy will *not* make any structural changes to the incoming request body (e.g., system prompt placement, message formatting) by default. The client is responsible for sending a body compatible with SuperWhisper's interpretation of OpenAI/Anthropic requests.
    *   **4.1.3. Conditional Request Transformation ("claude-cli" User-Agent):**
        *   If the `User-Agent` header of an incoming request *contains* the string "claude-cli":
            *   The proxy must transform the `messages` array in the request body. For each message object within this array:
                *   If the `content` field is an array of objects (e.g., `[{"text": "...", "type": "text"}, {"text": "...", "type": "text", "cache_control": ...}]`), it must be converted into a single string.
                *   This string will be formed by concatenating the `text` value of each object in the original `content` array.
                *   Individual `text` parts should be joined by a newline character (`\n`).
                *   The original `content` array in the message object must be replaced with this new concatenated string.
            *   This transformation applies *only* under this specific `User-Agent` condition. All other requests will bypass this logic.
    *   **4.1.4. Request to SuperWhisper API:**
        *   **Method:** POST
        *   **Headers:**
            *   `Content-Type: application/json`.
            *   Fixed operational headers required by SuperWhisper (e.g., `X-ID`, `X-License`, `X-Signature`, `User-Agent` for SuperWhisper). The values for these headers will be sourced from environment variables.
        *   **Body:**
            *   The (potentially transformed, if "claude-cli") request body received from the client will form the basis of the body sent to SuperWhisper.
            *   The `model` field in the body will be replaced with the mapped SuperWhisper model name (as per section 3.2).
            *   The `stream` parameter *must* be explicitly set to `true`.
            *   A `user` field (e.g., `user: "e58281a7b3539227281a17470e87c2d7"`) will be included in the body. The value for this `user` field will be sourced from an environment variable.
    *   **4.1.5. Response Handling from SuperWhisper API:**
        *   The SuperWhisper API will respond with a `text/event-stream`.
        *   The proxy server must consume and accumulate all data events from this stream until the stream is complete.
    *   **4.1.6. Response Construction to Client (AI Agent):**
        *   After accumulating the entire response from SuperWhisper, the proxy will construct a single, complete JSON object.
        *   This response object *must* emulate the standard *non-streaming* response format of the corresponding native API (OpenAI or Anthropic), based on the original model requested.
        *   The `Content-Type` of the response to the client shall be `application/json`.
        *   **Anthropic-style Response Structure (for `claude-3-5-sonnet-20241022`, `claude-3-7-sonnet-20250219`):**
            ```json
            {
              "id": "msg_...", // Derived from SuperWhisper's 'message_start' event's message.id
              "type": "message",
              "role": "assistant",
              "model": "claude-3-5-sonnet-20241022", // Actual model name from SuperWhisper stream (e.g., message_start.message.model)
              "content": [
                {
                  "type": "text",
                  "text": "Full accumulated text content from all 'content_block_delta' events"
                }
              ],
              "stop_reason": "end_turn", // Derived from SuperWhisper 'message_delta' event
              "stop_sequence": null,    // Derived from SuperWhisper 'message_delta' event (if present)
              "usage": {
                "input_tokens": 0,  // Derived from SuperWhisper 'message_start' event's message.usage.input_tokens
                "output_tokens": 0 // Derived from SuperWhisper 'message_delta' event's usage.output_tokens
              }
            }
            ```
        *   **OpenAI-style Response Structure (for `gpt-4.1-2025-04-14`):**
            ```json
            {
              "id": "chatcmpl-...", // Derived from SuperWhisper stream chunk's id
              "object": "chat.completion",
              "created": 1677652288, // Unix epoch timestamp (seconds) of when the proxy finalizes this response
              "model": "gpt-4.1-2025-04-14", // Actual model name from SuperWhisper stream chunk's model
              "choices": [
                {
                  "index": 0,
                  "message": {
                    "role": "assistant",
                    "content": "Full accumulated text content from all chunks' delta.content"
                  },
                  "finish_reason": "stop" // Derived from SuperWhisper stream chunk's finish_reason
                }
              ],
              "usage": { // This field will be omitted, or all token counts set to 0 or null.
                "prompt_tokens": 0,
                "completion_tokens": 0,
                "total_tokens": 0
              }
            }
            ```
*   **4.2. `/models` Endpoint (HTTP GET)**
    *   No authorization shall be required for this endpoint.
    *   It will return a hardcoded JSON response listing the three client-facing model IDs defined in section 3.1.
    *   The structure will be:
        ```json
        {
          "object": "list",
          "data": [
            {
              "id": "claude-3-5-sonnet-20241022",
              "object": "model",
              "created": 1700000000, // Static, arbitrary Unix timestamp
              "owned_by": "system"  // Static value
            },
            {
              "id": "claude-3-7-sonnet-20250219",
              "object": "model",
              "created": 1700000001, // Static, arbitrary Unix timestamp
              "owned_by": "system"
            },
            {
              "id": "gpt-4.1-2025-04-14",
              "object": "model",
              "created": 1700000002, // Static, arbitrary Unix timestamp
              "owned_by": "system"
            }
          ]
        }
        ```
*   **4.3. CORS Handling (HTTP OPTIONS)**
    *   The server must handle pre-flight OPTIONS requests for `/chat/completions` and `/models`.
    *   Responses to OPTIONS requests should include appropriate CORS headers:
        *   `Access-Control-Allow-Origin: *` (or a more specific origin if preferred)
        *   `Access-Control-Allow-Methods: GET, POST, OPTIONS`
        *   `Access-Control-Allow-Headers: Content-Type, Authorization`

**5. Error Handling**

*   **5.1. Client-Facing Errors:**
    *   The proxy shall return minimal, generic error messages to the client.
    *   For example: `HTTP 500 - {"error": "Internal Server Error"}`, `HTTP 400 - {"error": "Bad Request"}`.
    *   Authentication failures will result in `HTTP 401 - {"error": "Unauthorized"}`.
*   **5.2. Server-Side Error Logging:**
    *   All errors, whether originating from within the proxy (e.g., configuration issues) or from the SuperWhisper API (e.g., API errors, network failures), must be logged in detail on the server-side (see Section 6: Logging).

**6. Logging**

*   **6.1. Logging Level:** Verbose/Debug.
*   **6.2. Log Destination:** All log entries shall be written to a local log file (e.g., `proxy.log`). Simple file appending is acceptable.
*   **6.3. Log Content (for each request-response cycle):**
    *   Timestamp (ISO 8601 format).
    *   **Incoming Request from Client:**
        *   Method (e.g., POST, GET, OPTIONS).
        *   URL.
        *   Headers (including `User-Agent`, `Content-Type`, `Authorization` (value redacted or indicated as present)).
        *   Full request body (for POST requests).
    *   **Outgoing Request to SuperWhisper API:**
        *   Method (POST).
        *   Target URL.
        *   Headers sent.
        *   Full request body sent.
    *   **Response from SuperWhisper API:**
        *   Log key events from the `text/event-stream` (e.g., `message_start`, `content_block_start`, `content_block_delta` (log a sample or summary if too verbose, e.g., first and last chunk, or chunk count), `message_delta`, `message_stop`).
        *   Any error events or non-200 status codes from SuperWhisper.
    *   **Outgoing Response to Client:**
        *   HTTP status code.
        *   Full response body.
    *   **Errors Encountered:**
        *   Timestamp.
        *   Detailed error message.
        *   Stack trace (if applicable).
        *   Contextual information (e.g., at what stage of processing the error occurred).

**7. Configuration**

*   **7.1. Environment Variables (via `.env` file):**
    *   `PORT`: The port on which the proxy server will listen (e.g., `4000`).
    *   `PROXY_AUTH_TOKEN`: The Bearer token expected from clients for authorization.
    *   `SUPERWHISPER_API_BASE_URL_OPENAI`: Base URL for SuperWhisper's OpenAI-style endpoint (e.g., `https://api.superwhisper.com/v1`).
    *   `SUPERWHISPER_API_BASE_URL_ANTHROPIC`: Base URL for SuperWhisper's Anthropic-style endpoint (e.g., `https://api.superwhisper.com/anthropic/v1`).
    *   `SUPERWHISPER_HEADER_X_ID`: Value for the `X-ID` header sent to SuperWhisper.
    *   `SUPERWHISPER_HEADER_X_LICENSE`: Value for the `X-License` header.
    *   `SUPERWHISPER_HEADER_X_SIGNATURE`: Value for the `X-Signature` header.
    *   `SUPERWHISPER_USER_ID`: Value for the `user` field in the JSON body sent to SuperWhisper.
    *   *(Additional environment variables for other fixed SuperWhisper headers like `Accept-Language`, `Accept-Encoding`, `User-Agent` to SuperWhisper, etc. should be added as needed).*
*   **7.2. CLI Arguments:**
    *   The primary mechanism for runtime configuration (like port) is via environment variables.
    *   The `cli.js` will support a `--port <number>` argument, which can override the `PORT` environment variable.

**8. Technical Stack & Structure**

*   **8.1. Language/Platform:** Node.js (CommonJS modules, as per current setup).
*   **8.2. Core Dependencies:**
    *   `express`: Web server framework.
    *   `node-fetch` (version 2.x.x for CommonJS compatibility, unless project is set up for ESM): For making HTTP requests.
    *   `dotenv`: For loading environment variables from a `.env` file.
    *   `commander`: For parsing CLI arguments.
*   **8.3. Suggested File Structure (Example for clarity and modularity):**
    ```
    myllm-proxy/
    ├── .env                # Local environment variables (ignored by git)
    ├── .env.example        # Example environment file
    ├── .gitignore
    ├── package.json
    ├── cli.js              # Main entry point, CLI parsing, server initialization
    ├── server.js           # Express app: routes, core middleware (JSON parsing, CORS)
    │
    ├── src/
    │   ├── routes/
    │   │   ├── modelsRouter.js         # Handles /models endpoint
    │   │   └── chatCompletionsRouter.js # Handles /chat/completions
    │   │
    │   ├── middleware/
    │   │   ├── authMiddleware.js       # Validates PROXY_AUTH_TOKEN
    │   │   └── errorHandlingMiddleware.js # Centralized error response
    │   │
    │   ├── services/
    │   │   └── superwhisperClient.js   # Logic for calling SuperWhisper, stream accumulation
    │   │
    │   ├── transformations/
    │   │   └── claudeCliTransform.js   # Request body transformation for "claude-cli"
    │   │
    │   └── utils/
    │       ├── logger.js               # File logging utility
    │       └── responseFormatter.js    # Formats final OpenAI/Anthropic non-streamed JSON
    │
    └── proxy.log             # Output log file (ignored by git)
    ```
*   **8.4. Code Style & Practices:**
    *   Code must be clean, modular, and well-commented where non-obvious.
    *   Employ consistent code formatting (e.g., using Prettier).
    *   Utilize `async/await` for all asynchronous operations.
    *   Adhere to separation of concerns (e.g., routing logic separate from business/API logic).
    *   Use descriptive variable and function names.
    *   Avoid deeply nested callbacks.