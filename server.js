// server.js

// IMPORTANT: Load the logger first to initialize it, which may trigger the interactive prompt.
const logger = require('./src/utils/logger');

const express = require('express');
const cors = require('cors');
const modelsRouter = require('./src/routes/modelsRouter');
const chatCompletionsRouter = require('./src/routes/chatCompletionsRouter');
const { authenticateToken } = require('./src/middleware/authMiddleware');
const { ensureAllowedClient, claudeCodeRateLimiter, transformClaudeCliRequest } = require('./src/middleware/claudeCodeMiddleware');
const { globalErrorHandler } = require('./src/middleware/errorHandlingMiddleware');
const { logOriginalRequest } = require('./src/middleware/debugLoggingMiddleware'); // <-- IMPORT NEW MIDDLEWARE

const app = express();

// CORS Configuration
const corsOptions = {
  origin: '*',
  methods: ['GET', 'POST', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'User-Agent', 'Anthropic-Beta', 'Anthropic-Dangerous-Direct-Browser-Access', 'Anthropic-Version', 'X-Api-Key', 'X-App', 'X-Forwarded-For', 'X-Forwarded-Host', 'X-Forwarded-Proto', 'X-Stainless-Arch', 'X-Stainless-Lang', 'X-Stainless-Os', 'X-Stainless-Package-Version', 'X-Stainless-Retry-Count', 'X-Stainless-Runtime', 'X-Stainless-Runtime-Version', 'X-Stainless-Timeout', 'Sec-Fetch-Mode'],
  preflightContinue: true,
};
app.use(cors(corsOptions));

// Middleware to parse JSON bodies
app.use(express.json({ limit: '50mb' }));

// --- NEW PLACEMENT FOR DEBUG LOGGING ---
// Use our new middleware here to log the request BEFORE any transformations.
app.use(logOriginalRequest);

// Request/Response Logging Middleware (Simplified)
app.use((req, res, next) => {
  const basicLog = {
    timestamp: new Date().toISOString(),
    method: req.method,
    url: req.originalUrl,
    ip: req.ip,
    userAgent: req.headers['user-agent']
  };
  logger.info(`[REQ_INCOMING] ${basicLog.timestamp} ${basicLog.method} ${basicLog.url} from ${basicLog.ip}`, { headers: req.headers });
  next();
});

// Routes
app.use('/models', modelsRouter);

// Route for Claude Code clients (/v1/messages?beta=true)
const claudeCodeMiddlewareStack = [
  ensureAllowedClient,
  transformClaudeCliRequest,  // This middleware modifies req.body
  authenticateToken,
  claudeCodeRateLimiter,
];

app.post('/v1/messages', claudeCodeMiddlewareStack, (req, res, next) => {
  logger.info(`Forwarding authorized client request from ${req.originalUrl} to chatCompletionsRouter`, {
    clientIp: req.ip,
    clientUserAgent: req.headers['user-agent'],
    modelRequested: req.body.model,
  });

  const originalUrl = req.url;
  const originalBaseUrl = req.baseUrl;

  req.baseUrl = '/chat/completions';
  req.url = '/';

  chatCompletionsRouter(req, res, (err) => {
    req.url = originalUrl;
    req.baseUrl = originalBaseUrl;
    if (err) {
      logger.error('Error propagated from chatCompletionsRouter during Claude Code request forwarding.', {
        originalUrl: req.originalUrl,
        error: err.message,
        stack: err.stack
      });
      return next(err);
    }
  });
});

// Regular chat completions route
app.use('/chat/completions', chatCompletionsRouter);

// Catch-all for 404 Not Found routes
app.use((req, res, next) => {
  logger.warn(`404 Not Found: ${req.method} ${req.originalUrl}`, { ip: req.ip });
  res.status(404).json({ error: 'Not Found' });
});

// Global Error Handler
app.use(globalErrorHandler);

module.exports = app;