# Server Configuration
PORT=4000

# Proxy Authorization
PROXY_AUTH_TOKEN="your_secret_proxy_auth_token_here"

# SuperWhisper API Base URLs
SUPERWHISPER_API_BASE_URL_OPENAI="https://api.example.com/v1"
SUPERWHISPER_API_BASE_URL_ANTHROPIC="https://api.example.com/anthropic/v1"

# SuperWhisper API Fixed Headers & User ID
SUPERWHISPER_HEADER_X_ID="your_superwhisper_x_id"
SUPERWHISPER_HEADER_X_LICENSE="your_superwhisper_x_license_key"
SUPERWHISPER_HEADER_X_SIGNATURE="your_superwhisper_x_signature"

# SuperWhisper User-Agent (the User-Agent your proxy sends to SuperWhisper)
# This is different from the User-Agent your proxy *receives* from the client.
SUPERWHISPER_REQUEST_USER_AGENT="MyLLMProxy/1.0"


LOG_LEVEL="INFO" # DEBUG, INFO, WARN, ERROR

#Dry Run of Proxy Server to avoid sending request to Superwhisper API
DRY_RUN="false" # true or false