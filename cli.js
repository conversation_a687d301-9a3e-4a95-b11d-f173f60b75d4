#!/usr/bin/env node

require('dotenv').config();
const { Command } = require('commander');
const http = require('http');
const app = require('./server'); // Ensure server.js exports the app
const logger = require('./src/utils/logger');

const program = new Command();

program
  .version('1.0.0')
  .description('MyLLM Proxy Server')
  .option('-p, --port <number>', 'Port to run the server on', process.env.PORT || '4000')
  .parse(process.argv);

const options = program.opts();
const port = parseInt(options.port, 10);

if (isNaN(port)) {
  logger.error('Invalid port number provided. Exiting.');
  process.exit(1);
}

const server = http.createServer(app);

server.listen(port, () => {
  logger.info(`MyLLM Proxy Server listening on port ${port}`);
  logger.info(`OpenAI-style SuperWhisper API endpoint: ${process.env.SUPERWHISPER_API_BASE_URL_OPENAI}`);
  logger.info(`Anthropic-style SuperWhisper API endpoint: ${process.env.SUPERWHISPER_API_BASE_URL_ANTHROPIC}`);
  if (!process.env.PROXY_AUTH_TOKEN) {
    logger.warn('PROXY_AUTH_TOKEN is not set. The /chat/completions endpoint will be unprotected.');
  }
});

process.on('SIGTERM', () => {
  logger.info('SIGTERM signal received: closing HTTP server');
  server.close(() => {
    logger.info('HTTP server closed');
    process.exit(0);
  });
});

process.on('SIGINT', () => {
  logger.info('SIGINT signal received: closing HTTP server');
  server.close(() => {
    logger.info('HTTP server closed');
    process.exit(0);
  });
});