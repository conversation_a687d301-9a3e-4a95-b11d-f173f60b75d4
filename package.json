{"name": "myllm-proxy", "version": "1.0.0", "description": "A Node.js proxy server for MyLLM AI agents to interact with SuperWhisper API.", "main": "cli.js", "scripts": {"start": "node cli.js", "dev": "node cli.js --port 4001", "test": "jest", "test:watch": "jest --watch"}, "keywords": ["llm", "proxy", "superwhisper", "openai", "anthropic"], "author": "<PERSON>", "license": "ISC", "dependencies": {"commander": "^9.0.0", "cors": "^2.8.5", "dotenv": "^16.0.0", "express": "^4.17.1", "node-fetch": "^2.6.7", "readline-sync": "^1.4.10"}, "devDependencies": {"jest": "^29.7.0", "prettier": "^2.5.1", "supertest": "^7.0.0"}, "jest": {"testEnvironment": "node", "coveragePathIgnorePatterns": ["/node_modules/"]}}