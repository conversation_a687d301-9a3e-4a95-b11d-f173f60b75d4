const { transformRequestPayload } = require('./messageContentTransformer');
const logger = require('../utils/logger');

jest.mock('../utils/logger', () => ({
  warn: jest.fn(),
  debug: jest.fn(),
  error: jest.fn(),
  info: jest.fn(),
}));

describe('transformRequestPayload', () => {
  beforeEach(() => {
    logger.warn.mockClear();
    logger.debug.mockClear();
    logger.error.mockClear();
    logger.info.mockClear();
  });

  // Helper to create a deep copy for tests to avoid interference
  const deepCopy = (obj) => JSON.parse(JSON.stringify(obj));

  describe('Common Content Stringification (applies to all message types)', () => {
    const baseRequestBody = {
      messages: [
        { role: 'user', content: [{ type: 'text', text: 'Hello' }, { type: 'text', text: 'World' }] },
        { role: 'assistant', content: 'Already a string.' },
        { role: 'user', content: [{ type: 'image', source: '...' }, { type: 'text', text: 'Text with image.' }] },
        { role: 'user', content: [] }, // Empty content array
        { role: 'user', content: null }, // Null content
      ],
    };

    it('should stringify message content arrays for Anthropic endpoint', () => {
      const requestBody = deepCopy(baseRequestBody);
      transformRequestPayload(requestBody, 'anthropic');
      expect(requestBody.messages[0].content).toBe('Hello\nWorld');
      expect(requestBody.messages[1].content).toBe('Already a string.');
      expect(requestBody.messages[2].content).toBe('Text with image.');
      expect(logger.warn).toHaveBeenCalledWith(expect.stringContaining('original content array contained non-text or unhandled parts'), expect.any(Object));
      expect(requestBody.messages[3].content).toBe(''); // Empty array -> empty string
      expect(requestBody.messages[4].content).toBe(''); // Null content -> empty string
    });

    it('should stringify message content arrays for OpenAI endpoint', () => {
      const requestBody = deepCopy(baseRequestBody);
      // Add a system message to test its content stringification for OpenAI
      requestBody.messages.unshift({ role: 'system', content: [{ type: 'text', text: 'System rule 1' }, 'System rule 2'] });
      
      transformRequestPayload(requestBody, 'openai');
      expect(requestBody.messages[0].content).toBe('System rule 1\nSystem rule 2'); // System message content
      expect(requestBody.messages[1].content).toBe('Hello\nWorld'); // User message
      expect(requestBody.messages[2].content).toBe('Already a string.');
      expect(requestBody.messages[3].content).toBe('Text with image.');
       expect(requestBody.messages[4].content).toBe('');
      expect(requestBody.messages[5].content).toBe('');
    });
  });

  describe('Anthropic EndpointType System Prompt Handling', () => {
    it('should move system message from messages array to top-level system string', () => {
      const requestBody = {
        messages: [
          { role: 'system', content: 'System prompt here.' },
          { role: 'user', content: 'User query.' },
        ],
      };
      transformRequestPayload(requestBody, 'anthropic');
      expect(requestBody.system).toBe('System prompt here.');
      expect(requestBody.messages.length).toBe(1);
      expect(requestBody.messages[0].role).toBe('user');
      expect(logger.info).toHaveBeenCalledWith(expect.stringContaining('Consolidated system prompt for Anthropic endpoint.'), expect.any(Object));
    });

    it('should stringify system message content if it is an array', () => {
      const requestBody = {
        messages: [{ role: 'system', content: [{ type: 'text', text: 'Part 1' }, { type: 'text', text: 'Part 2' }] }],
      };
      transformRequestPayload(requestBody, 'anthropic');
      expect(requestBody.system).toBe('Part 1\nPart 2');
    });

    it('should combine top-level system and system-role message, stringifying array content', () => {
      const requestBody = {
        system: [{ type: 'text', text: 'Top-level system P1.' }, 'Top-level system P2.'],
        messages: [{ role: 'system', content: 'System-role message.' }, { role: 'user', content: 'Hi' }],
      };
      transformRequestPayload(requestBody, 'anthropic');
      expect(requestBody.system).toBe('Top-level system P1.\nTop-level system P2.\n\nSystem-role message.');
      expect(requestBody.messages.length).toBe(1);
      expect(requestBody.messages[0].role).toBe('user');
      expect(logger.info).toHaveBeenCalledWith(expect.stringContaining('Consolidated system prompt for Anthropic endpoint.'), expect.any(Object));
    });

    it('should handle multiple system-role messages', () => {
        const requestBody = {
            messages: [
                { role: 'system', content: 'First system message.' },
                { role: 'user', content: 'User query.' },
                { role: 'system', content: 'Second system message.' },
            ],
        };
        transformRequestPayload(requestBody, 'anthropic');
        expect(requestBody.system).toBe('First system message.\n\nSecond system message.');
        expect(requestBody.messages.length).toBe(1);
        expect(requestBody.messages[0].role).toBe('user');
    });

    it('should handle only top-level system prompt (string)', () => {
        const requestBody = { system: "Top level only.", messages: [{role: 'user', content: 'Hi'}]};
        transformRequestPayload(requestBody, 'anthropic');
        expect(requestBody.system).toBe("Top level only.");
        expect(requestBody.messages.length).toBe(1);
    });
  });

  describe('OpenAI EndpointType System Prompt Handling', () => {
    it('should move top-level system string into messages array', () => {
      const requestBody = {
        system: 'System prompt here.',
        messages: [{ role: 'user', content: 'User query.' }],
      };
      transformRequestPayload(requestBody, 'openai');
      expect(requestBody.system).toBeUndefined();
      expect(requestBody.messages.length).toBe(2);
      expect(requestBody.messages[0].role).toBe('system');
      expect(requestBody.messages[0].content).toBe('System prompt here.');
      expect(requestBody.messages[1].role).toBe('user');
      expect(logger.info).toHaveBeenCalledWith(expect.stringContaining('Moved top-level system prompt into messages array for OpenAI endpoint.'), expect.any(Object));
    });

    it('should stringify top-level system content if it is an array before moving', () => {
      const requestBody = {
        system: [{ type: 'text', text: 'SysP1' }, { type: 'text', text: 'SysP2' }],
        messages: [],
      };
      transformRequestPayload(requestBody, 'openai');
      expect(requestBody.system).toBeUndefined();
      expect(requestBody.messages.length).toBe(1);
      expect(requestBody.messages[0].role).toBe('system');
      expect(requestBody.messages[0].content).toBe('SysP1\nSysP2');
    });

    it('should keep system messages already in messages array as is (content stringified)', () => {
      const requestBody = {
        messages: [
          { role: 'system', content: [{ type: 'text', text: 'Sys In Array' }] },
          { role: 'user', content: 'User query.' },
        ],
      };
      transformRequestPayload(requestBody, 'openai');
      expect(requestBody.system).toBeUndefined(); // No top-level system to begin with
      expect(requestBody.messages.length).toBe(2);
      expect(requestBody.messages[0].role).toBe('system');
      expect(requestBody.messages[0].content).toBe('Sys In Array'); // Content gets stringified
    });
  });

  it('should return requestBody as is if it is null or undefined', () => {
    expect(transformRequestPayload(null, 'anthropic')).toBeNull();
    expect(transformRequestPayload(undefined, 'openai')).toBeUndefined();
  });

  it('should handle requestBody with no messages property gracefully', () => {
    const requestBody = { model: 'test-model' };
    const originalBody = deepCopy(requestBody);
    transformRequestPayload(requestBody, 'anthropic');
    expect(requestBody).toEqual(originalBody); // No .messages, so no change related to messages
  });
  
  it('should handle requestBody with non-array messages property gracefully', () => {
    const requestBody = { model: 'test-model', messages: "not-an-array" };
    const originalBody = deepCopy(requestBody);
    transformRequestPayload(requestBody, 'anthropic');
    expect(requestBody).toEqual(originalBody);
  });
});