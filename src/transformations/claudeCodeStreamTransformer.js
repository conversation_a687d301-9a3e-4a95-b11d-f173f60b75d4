// src/transformations/claudeCodeStreamTransformer.js

const logger = require('../utils/logger');

// Helper to generate a compliant tool ID
function generateToolId() {
  const S = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
  const idSuffix = Array.from({ length: 26 }, () => S[Math.floor(Math.random() * S.length)]).join('');
  return 'toolu_' + idSuffix;
}

/**
 * Parses a single <tool_use> XML string.
 * It attempts to parse parameter values as JSON.
 * @param {string} toolXml A string containing a single <tool_use>...</tool_use> block.
 * @returns {object|null} A structured tool object or null if parsing fails.
 */
function parseSingleToolXml(toolXml) {
  const toolNamePattern = /<name>([\s\S]*?)<\/name>/;
  const toolNameMatch = toolXml.match(toolNamePattern);
  if (!toolNameMatch || !toolNameMatch[1]) return null;

  const toolName = toolNameMatch[1].trim();
  const input = {};
  const toolParameterPattern = /<parameter name="([^"]+)">([\s\S]*?)<\/parameter>/g;
  let paramMatch;

  while ((paramMatch = toolParameterPattern.exec(toolXml)) !== null) {
    const paramName = paramMatch[1];
    const paramValue = paramMatch[2];
    
    try {
      // If the value is valid JSON (like for TodoWrite), parse it.
      input[paramName] = JSON.parse(paramValue);
      logger.debug(`Successfully parsed parameter "${paramName}" as JSON.`);
    } catch (e) {
      // If it's not JSON (like for Write/Edit), use the raw string.
      input[paramName] = paramValue;
    }
  }

  return {
    type: 'tool_use',
    id: generateToolId(),
    name: toolName,
    input: input,
  };
}


/**
 * The final, definitive transformer.
 * It handles all discovered edge cases:
 * - Handles malformed responses where the model returns a JSON array string instead of XML.
 * - Translates XML-based tool streams to JSON-delta streams.
 * - Passes plain text streams through correctly.
 * - Correctly parses JSON content within tool parameters.
 * - Handles multiple tool calls sent within a single content block.
 */
function transformClaudeCodeStream(superWhisperStream, clientResponse) {
  logger.info('Applying DEFINITIVE Claude Code transformation to SSE stream.');
  
  let sseBuffer = '';
  let finalUsage = {};
  let toolUseDetectedInAnyBlock = false;
  
  let currentBlock = {
    index: -1,
    bufferedEvents: [],
    fullText: "",
    isProcessing: false,
  };

  const writeSse = (event, data) => {
    if (clientResponse.writableEnded) return;
    
    try {
      const serializedData = JSON.stringify(data);
      clientResponse.write(`event: ${event}\ndata: ${serializedData}\n\n`);
    } catch (error) {
      logger.error('Error serializing SSE data', { 
        error: error.message, 
        event, 
        dataType: typeof data,
        dataKeys: data && typeof data === 'object' ? Object.keys(data) : 'N/A'
      });
      // Write a safe error event instead of crashing
      try {
        clientResponse.write(`event: error\ndata: ${JSON.stringify({ error: 'Serialization failed' })}\n\n`);
      } catch (fallbackError) {
        logger.error('Failed to write error event', { error: fallbackError.message });
      }
    }
  };

  const flushOriginalEvents = (block) => {
    logger.info(`Flushing original events for plain text block index ${block.index}.`);
    
    if (!block || !Array.isArray(block.bufferedEvents)) {
      logger.warn('Invalid block or bufferedEvents for flushing', { 
        blockExists: !!block,
        bufferedEventsType: block ? typeof block.bufferedEvents : 'N/A'
      });
      return;
    }
    
    block.bufferedEvents.forEach((eventBlock, index) => {
      try {
        if (!clientResponse.writableEnded && typeof eventBlock === 'string') {
          clientResponse.write(eventBlock + '\n\n');
        }
      } catch (error) {
        logger.error('Error writing buffered event', { 
          error: error.message, 
          eventIndex: index,
          eventType: typeof eventBlock
        });
      }
    });
  };

  /**
   * Helper: Takes a parsed tool object and emits the corresponding SSE stream events.
   * @param {object} parsedTool - The tool object with id, name, and input.
   * @param {number} writingIndex - The content block index to use for the events.
   */
  const emitStructuredToolStream = (parsedTool, writingIndex) => {
    if (!parsedTool) {
      logger.warn(`Skipping emission for null tool at index ${writingIndex}.`);
      return;
    }
    
    // Validate tool structure
    if (!parsedTool.name || typeof parsedTool.name !== 'string') {
      logger.warn(`Skipping tool with invalid name at index ${writingIndex}`, { 
        toolName: parsedTool.name,
        toolType: typeof parsedTool.name
      });
      return;
    }
    
    if (!parsedTool.id || typeof parsedTool.id !== 'string') {
      logger.warn(`Tool missing valid ID, generating new one`, { 
        toolName: parsedTool.name,
        originalId: parsedTool.id
      });
      parsedTool.id = generateToolId();
    }
    
    logger.info(`Emitting structured tool_use stream for "${parsedTool.name}" at index ${writingIndex}.`);
    
    try {
      const startBlockContent = { 
        type: 'tool_use', 
        id: parsedTool.id, 
        name: parsedTool.name, 
        input: {} 
      };
      writeSse('content_block_start', { 
        type: 'content_block_start', 
        index: writingIndex, 
        content_block: startBlockContent 
      });
      
      // Safe JSON serialization with fallback
      let jsonInputString;
      try {
        jsonInputString = JSON.stringify(parsedTool.input || {});
      } catch (serializationError) {
        logger.error('Error serializing tool input, using empty object', { 
          error: serializationError.message,
          toolName: parsedTool.name,
          inputType: typeof parsedTool.input
        });
        jsonInputString = '{}';
      }
      
      // Emit input in chunks with error handling
      for (let i = 0; i < jsonInputString.length; i += 20) {
        const chunk = jsonInputString.substring(i, i + 20);
        writeSse('content_block_delta', { 
          type: 'content_block_delta', 
          index: writingIndex, 
          delta: { 
            type: 'input_json_delta', 
            partial_json: chunk 
          } 
        });
      }
      
      writeSse('content_block_stop', { type: 'content_block_stop', index: writingIndex });
      
    } catch (error) {
      logger.error('Error emitting structured tool stream', { 
        error: error.message,
        toolName: parsedTool.name,
        writingIndex
      });
      
      // Emit a safe error block instead of crashing
      try {
        writeSse('content_block_start', { 
          type: 'content_block_start', 
          index: writingIndex, 
          content_block: { type: 'text', text: `Error processing tool: ${parsedTool.name}` } 
        });
        writeSse('content_block_stop', { type: 'content_block_stop', index: writingIndex });
      } catch (fallbackError) {
        logger.error('Failed to emit error block', { error: fallbackError.message });
      }
    }
  };


  /**
   * Processes a completed text block, detects the format (JSON array or XML),
   * finds all tool calls, and emits transformed events.
   */
  const processAndFlushBlock = (block) => {
    if (!block || typeof block.index !== 'number') {
      logger.error('Invalid block passed to processAndFlushBlock', { 
        blockExists: !!block,
        blockIndex: block ? block.index : 'N/A',
        blockType: typeof block
      });
      return;
    }
    
    let currentWritingIndex = block.index;
    let handled = false;
    
    try {

    // --- Improved JSON array detection with better validation ---
    try {
      const trimmedText = block.fullText.trim();
      
      // More robust heuristic check for JSON array format
      if (trimmedText.startsWith('[') && trimmedText.endsWith(']') && trimmedText.length > 2) {
        let potentialTools;
        
        try {
          potentialTools = JSON.parse(trimmedText);
        } catch (parseError) {
          logger.debug('Failed to parse potential JSON array, proceeding with XML parsing', { 
            error: parseError.message, 
            textSample: trimmedText.substring(0, 100) 
          });
          throw parseError; // Re-throw to trigger fallback
        }
        
        // Enhanced validation: Ensure it's an array with valid tool objects
        if (Array.isArray(potentialTools) && potentialTools.length > 0) {
          const validTools = potentialTools.filter(t => 
            t && 
            typeof t === 'object' && 
            t.type === 'tool_use' && 
            typeof t.name === 'string' && 
            t.name.trim().length > 0
          );
          
          if (validTools.length > 0) {
            logger.info(`Detected and successfully parsed malformed JSON array format. Processing ${validTools.length} valid tool objects out of ${potentialTools.length} total.`);
            toolUseDetectedInAnyBlock = true;

            for (const tool of validTools) {
              const parsedTool = {
                type: 'tool_use',
                id: (tool.id && typeof tool.id === 'string') ? tool.id : generateToolId(),
                name: tool.name.trim(),
                input: (tool.input && typeof tool.input === 'object') ? tool.input : {}
              };
              emitStructuredToolStream(parsedTool, currentWritingIndex);
              currentWritingIndex++;
            }
            handled = true;
          } else {
            logger.debug('JSON array found but contains no valid tool objects, proceeding with XML parsing');
          }
        } else {
          logger.debug('Parsed JSON is not a non-empty array, proceeding with XML parsing');
        }
      }
    } catch (e) {
      logger.debug("Content was not a valid JSON array of tools, will proceed with XML parsing.", { 
        error: e.message,
        textSample: block.fullText.substring(0, 50) 
      });
    }

    // --- FALLBACK: If not handled as JSON, use original XML logic ---
    if (!handled) {
      try {
        const toolUseRegex = /<tool_use>[\s\S]*?<\/tool_use>/g;
        let toolMatches;
        
        try {
          toolMatches = [...block.fullText.matchAll(toolUseRegex)];
        } catch (regexError) {
          logger.error('Error matching tool_use regex', { 
            error: regexError.message,
            textLength: block.fullText.length
          });
          toolMatches = [];
        }

        if (toolMatches.length === 0) {
          // It's plain text.
          flushOriginalEvents(block);
          writeSse('content_block_stop', { type: 'content_block_stop', index: block.index });
        } else {
          // It's a tool block (or multiple) in XML format.
          toolUseDetectedInAnyBlock = true;
          let lastIndex = 0;

          for (const match of toolMatches) {
            try {
              const textBefore = block.fullText.substring(lastIndex, match.index).trim();
              if (textBefore) {
                logger.info(`Emitting interstitial text at index ${currentWritingIndex}.`);
                writeSse('content_block_start', { 
                  type: 'content_block_start', 
                  index: currentWritingIndex, 
                  content_block: { type: 'text', text: textBefore } 
                });
                writeSse('content_block_stop', { type: 'content_block_stop', index: currentWritingIndex });
                currentWritingIndex++;
              }

              const toolXml = match[0];
              const parsedTool = parseSingleToolXml(toolXml);
              
              if (parsedTool) {
                emitStructuredToolStream(parsedTool, currentWritingIndex);
                currentWritingIndex++;
              } else {
                logger.warn(`Failed to parse a tool block at index ${currentWritingIndex}. Skipping.`, {
                  toolXmlSample: toolXml.substring(0, 100),
                  toolXmlLength: toolXml.length
                });
              }
              
              lastIndex = match.index + toolXml.length;
              
            } catch (matchError) {
              logger.error('Error processing tool match', { 
                error: matchError.message,
                matchIndex: match.index
              });
              continue; // Skip this match and continue with others
            }
          }

          try {
            const textAfter = block.fullText.substring(lastIndex).trim();
            if (textAfter) {
              logger.info(`Emitting trailing text at index ${currentWritingIndex}.`);
              writeSse('content_block_start', { 
                type: 'content_block_start', 
                index: currentWritingIndex, 
                content_block: { type: 'text', text: textAfter } 
              });
              writeSse('content_block_stop', { type: 'content_block_stop', index: currentWritingIndex });
            }
          } catch (textAfterError) {
            logger.error('Error processing trailing text', { error: textAfterError.message });
          }
        }
      } catch (xmlProcessingError) {
        logger.error('Error in XML processing fallback', { 
          error: xmlProcessingError.message,
          blockIndex: block.index
        });
        
        // Final fallback: just flush original events
        flushOriginalEvents(block);
        writeSse('content_block_stop', { type: 'content_block_stop', index: block.index });
      }
    }

    } catch (error) {
      logger.error('Error in processAndFlushBlock, falling back to original events', { 
        error: error.message,
        blockIndex: block.index,
        blockTextLength: block.fullText ? block.fullText.length : 0
      });
      
      // Fallback: flush original events if possible
      try {
        flushOriginalEvents(block);
        writeSse('content_block_stop', { type: 'content_block_stop', index: block.index });
      } catch (fallbackError) {
        logger.error('Fallback flush also failed', { error: fallbackError.message });
      }
    }

    // Reset for the next block
    currentBlock = { index: -1, bufferedEvents: [], fullText: "", isProcessing: false };
  };

  // --- Main Stream Processing Loop (no changes needed here) ---
  superWhisperStream.on('data', (chunk) => {
    if (clientResponse.writableEnded) return;
    
    // Safe string conversion with validation
    let chunkString;
    try {
      chunkString = chunk ? chunk.toString('utf8') : '';
      if (typeof chunkString !== 'string') {
        logger.warn('Chunk conversion to string failed, skipping chunk');
        return;
      }
    } catch (error) {
      logger.error('Error converting chunk to string', { error: error.message });
      return;
    }
    
    sseBuffer += chunkString;
    
    let eventBoundary;
    while ((eventBoundary = sseBuffer.indexOf('\n\n')) !== -1) {
      const eventBlock = sseBuffer.substring(0, eventBoundary);
      sseBuffer = sseBuffer.substring(eventBoundary + 2);

      let eventName = null, eventDataString = null;
      
      // Safe event block processing
      try {
        const lines = eventBlock.split('\n');
        lines.forEach(line => {
          if (line.startsWith('event:')) eventName = line.substring('event: '.length).trim();
          else if (line.startsWith('data:')) eventDataString = line.substring('data: '.length).trim();
        });
      } catch (error) {
        logger.error('Error processing event block lines', { error: error.message, eventBlock: eventBlock.substring(0, 100) });
        continue;
      }

      if (!eventName || !eventDataString) continue;

      try {
        const jsonData = JSON.parse(eventDataString);

        switch(eventName) {
          case 'message_start':
          case 'ping':
            clientResponse.write(eventBlock + '\n\n');
            break;
          case 'content_block_start':
            currentBlock.isProcessing = true;
            currentBlock.index = jsonData.index;
            currentBlock.bufferedEvents.push(eventBlock);
            break;
          case 'content_block_delta':
            if (currentBlock.isProcessing && jsonData.delta?.type === 'text_delta') {
              currentBlock.fullText += jsonData.delta.text;
              currentBlock.bufferedEvents.push(eventBlock);
            } else {
              clientResponse.write(eventBlock + '\n\n');
            }
            break;
          case 'content_block_stop':
            if (currentBlock.isProcessing && jsonData.index === currentBlock.index) {
              processAndFlushBlock(currentBlock);
            } else {
               clientResponse.write(eventBlock + '\n\n');
            }
            break;
          case 'message_delta':
            if (jsonData.usage) Object.assign(finalUsage, jsonData.usage);
            break;
          case 'message_stop':
            const stopReason = toolUseDetectedInAnyBlock ? 'tool_use' : 'end_turn';
            logger.info(`Upstream ended. Sending final message_delta with stop_reason: ${stopReason}`);
            writeSse('message_delta', { type: 'message_delta', delta: { stop_reason: stopReason, stop_sequence: null }, usage: finalUsage });
            writeSse('message_stop', { type: 'message_stop' });
            if (!clientResponse.writableEnded) clientResponse.end();
            return;
        }
      } catch (e) {
        logger.error('Error in stream transformer, passing event through.', { error: e.message, data: eventDataString });
        clientResponse.write(eventBlock + '\n\n');
      }
    }
  });

  superWhisperStream.on('error', (err) => logger.error('Error on SuperWhisper stream', { message: err.message }));
  superWhisperStream.on('end', () => {
    logger.info('SuperWhisper stream ended.');
    if (!clientResponse.writableEnded) {
      clientResponse.end();
    }
  });
}

module.exports = { transformClaudeCodeStream };