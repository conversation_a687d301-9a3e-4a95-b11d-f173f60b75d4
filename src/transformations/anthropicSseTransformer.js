const logger = require('../utils/logger');

/**
 * Transforms a readable stream of Anthropic SSE events into OpenAI-compatible SSE events
 * and writes them to the clientResponse. This is specifically for the "Cursor" client.
 *
 * @param {ReadableStream} superWhisperStream The raw readable stream from SuperWhisper (Anthropic).
 * @param {ServerResponse} clientResponse The Express response object to write to.
 * @param {string} clientRequestedModel The original model name requested by the client.
 */
function transformAnthropicStream(superWhisperStream, clientResponse, clientRequestedModel) {
  logger.info('Applying SPECIAL Anthropic SSE to OpenAI SSE transformation for Cursor client.');
  let sseBuffer = '';
  let openAIStreamId = `chatcmpl-anthropic-cursor-${Date.now()}`;
  let assistantRoleSent = false;
  let stopReasonFromAnthropic = null;
  let finalEventSent = false; // To track if [DONE] has been sent

  // New variables for token counts
  let inputTokens = 0;
  let outputTokens = 0;

  superWhisperStream.on('data', (chunk) => {
    if (finalEventSent || clientResponse.writableEnded) return;

    sseBuffer += chunk.toString('utf8');
    let eventBoundary;
    while ((eventBoundary = sseBuffer.indexOf('\n\n')) !== -1) {
      if (finalEventSent || clientResponse.writableEnded) break;
      const eventBlock = sseBuffer.substring(0, eventBoundary);
      sseBuffer = sseBuffer.substring(eventBoundary + 2);

      let currentEventName = null;
      let currentEventData = null;
      eventBlock.split('\n').forEach(line => {
        if (line.startsWith('event:')) {
          currentEventName = line.substring('event: '.length).trim();
        } else if (line.startsWith('data:')) {
          currentEventData = line.substring('data: '.length).trim();
        }
      });

      if (!currentEventName || !currentEventData) continue;

      try {
        const jsonData = JSON.parse(currentEventData);
        let choices = [];
        let openAICreated = Math.floor(Date.now() / 1000);
        let usage = null; // Default to null, only set for final chunk

        if (currentEventName === 'message_start') {
          if (jsonData.message && jsonData.message.id) openAIStreamId = jsonData.message.id;
          // Capture input_tokens
          if (jsonData.message && jsonData.message.usage && jsonData.message.usage.input_tokens !== undefined) {
            inputTokens = jsonData.message.usage.input_tokens;
            logger.debug(`AnthropicSSETransform: Captured input_tokens: ${inputTokens}`);
          }
          // Send role in first chunk
          if (!assistantRoleSent) {
            const roleChunk = {
              id: openAIStreamId,
              object: 'chat.completion.chunk',
              created: openAICreated,
              model: clientRequestedModel,
              choices: [{ index: 0, delta: { role: 'assistant' }, finish_reason: null }]
            };
            clientResponse.write(`data: ${JSON.stringify(roleChunk)}\n\n`);
            assistantRoleSent = true;
          }
          return; // Don't send any other chunks for message_start
        } else if (currentEventName === 'content_block_delta' && jsonData.delta && jsonData.delta.type === 'text_delta' && jsonData.delta.text) {
          choices.push({ index: 0, delta: { content: jsonData.delta.text }, finish_reason: null });
        } else if (currentEventName === 'message_delta') {
          if (jsonData.delta && jsonData.delta.stop_reason) {
            stopReasonFromAnthropic = jsonData.delta.stop_reason;
          }
          // Capture output_tokens regardless of stop_reason
          if (jsonData.usage && jsonData.usage.output_tokens !== undefined) {
            outputTokens = jsonData.usage.output_tokens;
            logger.debug(`AnthropicSSETransform: Captured output_tokens: ${outputTokens}`);
          }
        } else if (currentEventName === 'message_stop') {
          // Send final chunk with usage and [DONE]
          const finalChunk = {
            id: openAIStreamId,
            object: 'chat.completion.chunk',
            created: openAICreated,
            model: clientRequestedModel,
            choices: [{ index: 0, delta: {}, finish_reason: stopReasonFromAnthropic || 'stop' }],
            usage: {
              prompt_tokens: inputTokens,
              completion_tokens: outputTokens,
              total_tokens: inputTokens + outputTokens
            }
          };
          clientResponse.write(`data: ${JSON.stringify(finalChunk)}\n\n`);
          clientResponse.write('data: [DONE]\n\n');
          finalEventSent = true;
          return;
        } else if (currentEventName === 'ping') {
          if (!clientResponse.writableEnded) clientResponse.write(':\n\n');
          continue; // Skip writing an OpenAI chunk for pings
        }

        // Only write non-final chunks here (without usage)
        if (choices.length > 0 && !clientResponse.writableEnded) {
          const openAIChunk = { id: openAIStreamId, object: 'chat.completion.chunk', created: openAICreated, model: clientRequestedModel, choices: choices };
          // Do NOT add usage to intermediate chunks
          clientResponse.write(`data: ${JSON.stringify(openAIChunk)}\n\n`);
        }

      } catch (e) {
        logger.warn('Error parsing Anthropic event data or constructing OpenAI chunk during Cursor transformation', { error: e.message, data: currentEventData });
      }
    }
  });

  superWhisperStream.on('end', () => {
    logger.info('SuperWhisper stream (for Cursor transform) ended.');
    // Only send final chunk if we haven't already sent one
    if (!finalEventSent && !clientResponse.writableEnded) {
      const finalChoice = { index: 0, delta: {}, finish_reason: stopReasonFromAnthropic || 'stop' };
      const finalChunk = {
        id: openAIStreamId,
        object: 'chat.completion.chunk',
        created: Math.floor(Date.now() / 1000),
        model: clientRequestedModel,
        choices: [finalChoice],
        usage: { // Add usage to final chunk
          prompt_tokens: inputTokens,
          completion_tokens: outputTokens,
          total_tokens: inputTokens + outputTokens
        }
      };
      clientResponse.write(`data: ${JSON.stringify(finalChunk)}\n\n`);
      clientResponse.write('data: [DONE]\n\n');
      finalEventSent = true;
    }
    clientResponse.end();
  });

  superWhisperStream.on('error', (streamError) => {
    logger.error('Error on SuperWhisper stream during Cursor transformation', { message: streamError.message });
    if (!clientResponse.writableEnded) {
        // Optionally send an error event to the client if the SSE stream has started
        // clientResponse.write(`event: error\ndata: ${JSON.stringify({message: "Upstream error"})}\n\n`);
        clientResponse.end();
    }
  });
}

module.exports = { transformAnthropicStream };
