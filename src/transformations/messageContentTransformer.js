const logger = require('../utils/logger');

/**
 * Helper function to stringify an array of content parts into a single string.
 * Extracts text from elements that are strings or objects with a 'text' property
 * (specifically if type is 'text'). Non-text elements are ignored.
 *
 * @param {Array} contentPartsArray The array of content parts.
 * @param {string} contextForLogging A string describing the context (e.g., "message index 0", "system prompt") for logging.
 * @returns {string} The concatenated string content.
 */
function stringifyContentArray(contentPartsArray, contextForLogging) {
  if (!Array.isArray(contentPartsArray)) {
    logger.error(`stringifyContentArray called with non-array: ${typeof contentPartsArray}`, { contextForLogging });
    return (typeof contentPartsArray === 'string') ? contentPartsArray : ""; // Basic safety
  }

  let textParts = [];
  let hasNonTextContent = false;
  let originalContentStructureForLog = [];

  contentPartsArray.forEach(part => {
    originalContentStructureForLog.push(typeof part === 'string' ? 'string' : (part && part.type ? part.type : 'object'));
    if (typeof part === 'string') {
      textParts.push(part);
    } else if (typeof part === 'object' && part !== null && typeof part.text === 'string') {
      if (part.type === 'text') { // Strictly check for type: 'text' as per common conventions
        textParts.push(part.text);
      } else {
        hasNonTextContent = true;
        logger.debug(`MessageContentTransform: For ${contextForLogging}, content part of type '${part.type}' (with a text property) was ignored as type is not 'text'.`, { partType: part.type });
      }
    } else {
      hasNonTextContent = true;
      // No specific log here, covered by the general 'hasNonTextContent' warning
    }
  });

  if (hasNonTextContent) {
    logger.warn(`MessageContentTransform: For ${contextForLogging}, original content array contained non-text or unhandled parts which were ignored. Original structure: [${originalContentStructureForLog.join(', ')}]`, { originalStructure: originalContentStructureForLog.join(', '), context: contextForLogging });
  }
  
  const newTextContent = textParts.join('\n');
  if (contentPartsArray.length > 0) { // Only log transformation details if there was an array to transform
    logger.debug(`MessageContentTransform: For ${contextForLogging}, processed content array. Original parts: ${contentPartsArray.length}, Concatenated text length: ${newTextContent.length}. Original structure: [${originalContentStructureForLog.join(', ')}]`, { originalPartsCount: contentPartsArray.length, concatenatedLength: newTextContent.length, originalStructure: originalContentStructureForLog.join(', '), context: contextForLogging });
  }
  
  return newTextContent;
}

/**
 * Transforms a single content value (string or array) into a string.
 * @param {*} content The content to transform.
 * @param {string} contextForLogging Logging context.
 * @returns {string} The stringified content.
 */
function transformSingleContentToString(content, contextForLogging) {
    if (typeof content === 'string') {
        return content;
    }
    if (Array.isArray(content)) {
        return stringifyContentArray(content, contextForLogging);
    }
    if (content === null || content === undefined) {
        return ""; // Represent null/undefined content as an empty string.
    }
    logger.warn(`MessageContentTransform: Content for ${contextForLogging} is of unexpected type: ${typeof content}. Was handled as empty string.`, { contentType: typeof content, context: contextForLogging, originalContent: content });
    return "";
}

/**
 * Transforms the request body for SuperWhisper:
 * - Handles system prompt placement based on endpointType.
 * - Converts message content arrays to strings for specific clients (Claude Code, Zed).
 * - Modifies the requestBody object in place and returns it.
 *
 * @param {object} requestBody The client's request body.
 * @param {string} endpointType 'anthropic' or 'openai'.
 * @param {object} req The Express request object (optional, for user agent detection).
 * @returns {object} The modified requestBody.
 */
function transformRequestPayload(requestBody, endpointType, req = null) {
  if (!requestBody) return requestBody;

  // --- ANTHROPIC-SPECIFIC TRANSFORMATIONS ---
  if (endpointType === 'anthropic') {
    const collectedSystemParts = [];
    const userAndAssistantMessages = [];

    // Step 1: Collect all system content without transforming its structure.
    if (requestBody.system !== undefined) {
      if (Array.isArray(requestBody.system)) {
        collectedSystemParts.push(...requestBody.system);
      } else if (typeof requestBody.system === 'string' && requestBody.system.trim() !== '') {
        collectedSystemParts.push(requestBody.system);
      }
      delete requestBody.system;
    }

    if (Array.isArray(requestBody.messages)) {
      requestBody.messages.forEach((message) => {
        if (message.role === 'system') {
          // Add the content directly, don't stringify it.
          if (Array.isArray(message.content)) {
            collectedSystemParts.push(...message.content);
          } else if (typeof message.content === 'string' && message.content.trim() !== '') {
            collectedSystemParts.push(message.content);
          }
        } else {
          // Keep non-system messages for later processing.
          userAndAssistantMessages.push(message);
        }
      });
      requestBody.messages = userAndAssistantMessages;
    }

    // Step 2: Reconstruct the final system prompt, preserving its structure.
    if (collectedSystemParts.length > 0) {
      const hasContentBlocks = collectedSystemParts.some(part => typeof part === 'object' && part !== null);

      if (hasContentBlocks) {
        // If any part is an object (a content block), the entire system prompt must be an array of blocks.
        // We convert any string parts to the standard text block format.
        requestBody.system = collectedSystemParts.map(part => (typeof part === 'string' ? { type: 'text', text: part } : part));
        logger.info(`MessageContentTransform: Reconstructed system prompt as an array of content blocks.`, { numBlocks: requestBody.system.length });
      } else {
        // If all parts were strings, we can safely join them into a single string.
        requestBody.system = collectedSystemParts.join('\n\n');
        logger.info(`MessageContentTransform: Consolidated system prompt as a single string.`, { length: requestBody.system.length });
      }
    }
    // Note: The common message transformation at the end will now only process user/assistant messages for Anthropic.

  // --- OPENAI-SPECIFIC TRANSFORMATIONS ---
  } else if (endpointType === 'openai') {
    if (requestBody.system !== undefined) {
      // For OpenAI, we always stringify the system prompt and move it into the messages array.
      const systemContent = transformSingleContentToString(requestBody.system, "top-level system prompt");
      if (Array.isArray(requestBody.messages)) {
        requestBody.messages.unshift({ role: 'system', content: systemContent });
      } else {
        requestBody.messages = [{ role: 'system', content: systemContent }];
      }
      logger.info(`MessageContentTransform: Moved top-level system prompt into messages array for OpenAI endpoint.`, {});
      delete requestBody.system;
    }
  }

  // Common final step: Transform content of all messages remaining in the messages array.
  // For Anthropic, this now correctly applies only to user/assistant messages.
  // For OpenAI, this applies to all messages, including the system message we just moved.
  if (Array.isArray(requestBody.messages)) {
    requestBody.messages.forEach((message, index) => {
      message.content = transformSingleContentToString(message.content, `${message.role} role message at current index ${index}`);
    });
  }
  
  return requestBody;
}

module.exports = { transformRequestPayload };