// anthropicSseTransformer.test.js
const { transformAnthropicStream } = require('./anthropicSseTransformer');
const { Readable } = require('stream');
const logger = require('../utils/logger');

// Mock logger
jest.mock('../utils/logger', () => ({
  info: jest.fn(),
  warn: jest.fn(),
  error: jest.fn(),
  debug: jest.fn(), // Mock debug for new token logging
}));

// Mock Date.now() for consistent timestamps
const mockDateNow = jest.spyOn(Date, 'now');
const mockTimestampSecs = 1700000000; // Arbitrary fixed timestamp for testing

describe('Anthropic SSE Transformer', () => {
  let mockResponse;
  let mockStream;
  const mockModelName = 'claude-3-5-sonnet-20241022'; // Use the client-facing model name

  // Set timeout for all tests
  jest.setTimeout(10000);

  beforeEach(() => {
    // Reset mocks
    jest.clearAllMocks();
    mockDateNow.mockReturnValue(mockTimestampSecs * 1000); // Convert seconds to milliseconds

    // Mock response object with write/end methods
    mockResponse = {
      write: jest.fn(),
      end: jest.fn(),
      // Mock writableEnded to accurately simulate Express response behavior
      get writableEnded() {
        return this.end.mock.calls.length > 0;
      }
    };
  });

  afterAll(() => {
    mockDateNow.mockRestore(); // Restore Date.now() after all tests
  });

  describe('Basic transformation and Usage Object', () => {
    it('should transform message_start event and NOT include usage in initial chunk', (done) => {
      mockStream = new Readable({
        read() {
          // This simulates a scenario where only message_start and then stream end occurs.
          // This should result in:
          // 1. Role-only chunk (explicitly written by message_start handler)
          // 2. Final chunk with usage (from on('end') because no message_stop was received)
          // 3. [DONE] message
          this.push('event: message_start\ndata: {"type":"message_start","message":{"id":"msg_123","type":"message","role":"assistant","model":"claude-3-5-haiku-20241022","content":[],"stop_reason":null,"stop_sequence":null,"usage":{"input_tokens":20,"output_tokens":1}}}\n\n');
          this.push(null); // End the stream immediately
        }
      });

      transformAnthropicStream(mockStream, mockResponse, mockModelName);

      mockStream.on('end', () => {
        // EXPECTED: 3 calls (role chunk, final usage chunk, DONE)
        expect(mockResponse.write).toHaveBeenCalledTimes(3); 
        
        const roleChunkLine = mockResponse.write.mock.calls[0][0];
        const roleChunk = JSON.parse(roleChunkLine.substring('data: '.length));
        expect(roleChunk).toEqual({
          id: 'msg_123',
          object: 'chat.completion.chunk',
          created: mockTimestampSecs,
          model: mockModelName,
          choices: [{ index: 0, delta: { role: 'assistant' }, finish_reason: null }]
        });
        expect(roleChunk).not.toHaveProperty('usage');

        // Verify the final chunk from on('end') has usage
        const finalChunkLine = mockResponse.write.mock.calls[1][0]; // Should be the second call
        const finalChunk = JSON.parse(finalChunkLine.substring('data: '.length));
        expect(finalChunk).toHaveProperty('usage', {
            prompt_tokens: 20,
            completion_tokens: 0, // In this test case, output_tokens is 0 because no message_delta occurred
            total_tokens: 20
        });
        expect(finalChunk.choices[0].delta).toEqual({}); // Final chunk has empty delta
        expect(finalChunk.choices[0].finish_reason).toBe('stop');

        // Verify [DONE] is the last call
        expect(mockResponse.write.mock.calls[2][0]).toBe('data: [DONE]\n\n');

        expect(mockResponse.end).toHaveBeenCalled();
        done();
      });
    });

    it('should handle content_block_delta events and NOT include usage in intermediate chunks', (done) => {
      mockStream = new Readable({
        read() {
          // This simulates: message_start -> content_delta -> content_delta -> stream end
          // This should result in:
          // 1. Role-only chunk (from message_start)
          // 2. "Hello" content chunk
          // 3. " World" content chunk
          // 4. Final chunk with usage (from on('end') because no message_stop)
          // 5. [DONE] message
          this.push('event: message_start\ndata: {"type":"message_start","message":{"id":"msg_123","usage":{"input_tokens":20}}}\n\n');
          this.push('event: content_block_delta\ndata: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"Hello"}}\n\n');
          this.push('event: content_block_delta\ndata: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":" World"}}\n\n');
          this.push(null); // End the stream immediately
        }
      });

      transformAnthropicStream(mockStream, mockResponse, mockModelName);

      mockStream.on('end', () => {
        // EXPECTED: 5 calls (role, Hello, World, Final (end), DONE)
        expect(mockResponse.write).toHaveBeenCalledTimes(5); 

        const chunks = mockResponse.write.mock.calls
            .filter(call => call[0].startsWith('data: {"id":')) // Filter out DONE and pings
            .map(call => JSON.parse(call[0].substring('data: '.length)));

        expect(chunks.length).toBe(4); // Expect four data chunks (role, content1, content2, final)
        
        // Role chunk
        expect(chunks[0].choices[0].delta).toHaveProperty('role', 'assistant');
        expect(chunks[0]).not.toHaveProperty('usage');
        
        // Content chunks
        expect(chunks[1].choices[0].delta).toHaveProperty('content', 'Hello');
        expect(chunks[1]).not.toHaveProperty('usage');
        expect(chunks[2].choices[0].delta).toHaveProperty('content', ' World');
        expect(chunks[2]).not.toHaveProperty('usage');

        // Final chunk (from on('end'))
        expect(chunks[3]).toHaveProperty('usage', {
            prompt_tokens: 20,
            completion_tokens: 0, // No message_delta to set output_tokens
            total_tokens: 20
        });
        expect(chunks[3].choices[0].delta).toEqual({}); // Empty delta for final chunk
        expect(chunks[3].choices[0].finish_reason).toBe('stop');

        // Verify [DONE] is the last call
        expect(mockResponse.write.mock.calls[4][0]).toBe('data: [DONE]\n\n');

        expect(mockResponse.end).toHaveBeenCalled();
        done();
      });
    });

    it('should include usage in the final chunk when message_delta and message_stop are present', (done) => {
      mockStream = new Readable({
        read() {
          // This simulates: message_start -> content_delta -> message_delta -> message_stop -> stream end
          // This should result in:
          // 1. Role-only chunk (from message_start)
          // 2. "Final part." content chunk
          // 3. Final chunk with usage (from message_stop handler)
          // 4. [DONE] message
          this.push('event: message_start\ndata: {"type":"message_start","message":{"id":"msg_123","usage":{"input_tokens":25}}}\n\n');
          this.push('event: content_block_delta\ndata: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"Final part."}}\n\n');
          this.push('event: message_delta\ndata: {"type":"message_delta","delta":{"stop_reason":"end_turn","stop_sequence":null},"usage":{"output_tokens":50}}\n\n');
          this.push('event: message_stop\ndata: {"type":"message_stop"}\n\n');
          this.push(null);
        }
      });

      transformAnthropicStream(mockStream, mockResponse, mockModelName);

      mockStream.on('end', () => {
        // EXPECTED: 4 calls (role, content, final usage, DONE)
        expect(mockResponse.write).toHaveBeenCalledTimes(4); 
        
        const allCalls = mockResponse.write.mock.calls.map(call => call[0]);
        
        // First chunk should be role
        const roleChunk = JSON.parse(allCalls[0].substring('data: '.length));
        expect(roleChunk.choices[0].delta).toHaveProperty('role', 'assistant');
        expect(roleChunk).not.toHaveProperty('usage');

        // Second chunk should be content
        const contentChunk = JSON.parse(allCalls[1].substring('data: '.length));
        expect(contentChunk.choices[0].delta).toHaveProperty('content', 'Final part.');
        expect(contentChunk).not.toHaveProperty('usage');

        // The final chunk is the second to last write call (before [DONE])
        const finalContentChunkLine = allCalls[allCalls.length - 2]; 
        expect(finalContentChunkLine).toBeDefined();

        const finalChunk = JSON.parse(finalContentChunkLine.substring('data: '.length));
        
        expect(finalChunk).toEqual(expect.objectContaining({
            id: 'msg_123',
            object: 'chat.completion.chunk',
            created: mockTimestampSecs,
            model: mockModelName,
            choices: expect.arrayContaining([
                expect.objectContaining({
                    index: 0,
                    delta: {}, // Delta is empty in final chunk
                    finish_reason: 'end_turn'
                })
            ]),
            usage: {
                prompt_tokens: 25,
                completion_tokens: 50,
                total_tokens: 75
            }
        }));
        
        // Ensure the [DONE] message is sent last
        expect(mockResponse.write.mock.calls[mockResponse.write.mock.calls.length - 1][0]).toBe('data: [DONE]\n\n');
        expect(logger.debug).toHaveBeenCalledWith('AnthropicSSETransform: Captured input_tokens: 25');
        expect(logger.debug).toHaveBeenCalledWith('AnthropicSSETransform: Captured output_tokens: 50');
        expect(mockResponse.end).toHaveBeenCalled();
        done();
      });
    });

    it('should include usage with default stop reason if stream ends without message_delta and message_stop', (done) => {
      mockStream = new Readable({
        read() {
          // This simulates: message_start -> content_delta -> stream end (no message_delta, no message_stop)
          // This should result in:
          // 1. Role-only chunk (from message_start)
          // 2. "Hello." content chunk
          // 3. Final chunk with usage (from on('end') handler)
          // 4. [DONE] message
          this.push('event: message_start\ndata: {"type":"message_start","message":{"id":"msg_456","usage":{"input_tokens":10}}}\n\n');
          this.push('event: content_block_delta\ndata: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"Hello."}}\n\n');
          this.push(null); // Stream ends without explicit message_delta or message_stop
        }
      });

      transformAnthropicStream(mockStream, mockResponse, mockModelName);

      mockStream.on('end', () => {
        // EXPECTED: 4 calls (role, content, final usage, DONE)
        expect(mockResponse.write).toHaveBeenCalledTimes(4); 

        const allCalls = mockResponse.write.mock.calls.map(call => call[0]);
        
        // First chunk should be role
        const roleChunk = JSON.parse(allCalls[0].substring('data: '.length));
        expect(roleChunk.choices[0].delta).toHaveProperty('role', 'assistant');
        expect(roleChunk).not.toHaveProperty('usage');

        // Second chunk should be content
        const contentChunk = JSON.parse(allCalls[1].substring('data: '.length));
        expect(contentChunk.choices[0].delta).toHaveProperty('content', 'Hello.');
        expect(contentChunk).not.toHaveProperty('usage');

        const finalContentChunkLine = allCalls[allCalls.length - 2]; // Second to last before DONE
        expect(finalContentChunkLine).toBeDefined();

        const finalChunk = JSON.parse(finalContentChunkLine.substring('data: '.length));
        
        expect(finalChunk).toEqual(expect.objectContaining({
            id: 'msg_456',
            object: 'chat.completion.chunk',
            created: mockTimestampSecs,
            model: mockModelName,
            choices: expect.arrayContaining([
                expect.objectContaining({
                    index: 0,
                    delta: {},
                    finish_reason: 'stop' // Default stop reason
                })
            ]),
            usage: {
                prompt_tokens: 10,
                completion_tokens: 0, // No output tokens captured
                total_tokens: 10
            }
        }));
        // Verify [DONE] is the last call
        expect(mockResponse.write.mock.calls[mockResponse.write.mock.calls.length - 1][0]).toBe('data: [DONE]\n\n');
        expect(logger.debug).toHaveBeenCalledWith('AnthropicSSETransform: Captured input_tokens: 10');
        expect(mockResponse.end).toHaveBeenCalled();
        done();
      });
    });
  });

  describe('Stream completion', () => {
    it('should handle stream completion with stop reason', (done) => {
      mockStream = new Readable({
        read() {
          // This simulates: message_start -> content_delta -> message_delta (with stop_reason) -> message_stop -> stream end
          // This should result in:
          // 1. Role-only chunk
          // 2. "Hello" content chunk
          // 3. Final chunk with usage (from message_stop handler)
          // 4. [DONE] message
          this.push('event: message_start\ndata: {"type":"message_start","message":{"id":"msg_123","usage":{"input_tokens":1}}}\n\n');
          this.push('event: content_block_delta\ndata: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"Hello"}}\n\n');
          this.push('event: message_delta\ndata: {"type":"message_delta","delta":{"stop_reason":"end_turn"},"usage":{"output_tokens":2}}\n\n');
          this.push('event: message_stop\ndata: {"type":"message_stop"}\n\n');
          this.push(null);
        }
      });

      transformAnthropicStream(mockStream, mockResponse, mockModelName);

      mockStream.on('end', () => {
        // EXPECTED: 4 calls (Role, Content, Final, DONE)
        expect(mockResponse.write).toHaveBeenCalledTimes(4);
        
        const allCalls = mockResponse.write.mock.calls.map(call => call[0]);
        const finalChunkString = allCalls[allCalls.length - 2]; // Second to last call before [DONE]
        const finalChunk = JSON.parse(finalChunkString.substring('data: '.length));
        
        // Should have finish_reason in final chunk
        expect(finalChunk.choices[0].finish_reason).toBe('end_turn');
        
        // Should have usage in final chunk
        expect(finalChunk.usage).toEqual({
          prompt_tokens: 1,
          completion_tokens: 2,
          total_tokens: 3
        });
        // Should send [DONE]
        expect(mockResponse.write).toHaveBeenCalledWith('data: [DONE]\n\n');
        expect(mockResponse.end).toHaveBeenCalled();
        done();
      });
    });

    it('should handle stream completion without stop reason (from message_delta) but with message_stop', (done) => {
      mockStream = new Readable({
        read() {
          // This simulates: message_start -> content_delta -> message_delta (no stop_reason) -> message_stop -> stream end
          // This should result in:
          // 1. Role-only chunk
          // 2. "Hello" content chunk
          // 3. Final chunk with usage (from message_stop handler)
          // 4. [DONE] message
          this.push('event: message_start\ndata: {"type":"message_start","message":{"id":"msg_123","usage":{"input_tokens":1}}}\n\n');
          this.push('event: content_block_delta\ndata: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"Hello"}}\n\n');
          this.push('event: message_delta\ndata: {"type":"message_delta","delta":{},"usage":{"output_tokens":2}}\n\n'); // No stop_reason here
          this.push('event: message_stop\ndata: {"type":"message_stop"}\n\n');
          this.push(null);
        }
      });

      transformAnthropicStream(mockStream, mockResponse, mockModelName);

      mockStream.on('end', () => {
        // EXPECTED: 4 calls (Role, Content, Final, DONE)
        expect(mockResponse.write).toHaveBeenCalledTimes(4);
        
        const allCalls = mockResponse.write.mock.calls.map(call => call[0]);
        const finalChunkString = allCalls[allCalls.length - 2];
        const finalChunk = JSON.parse(finalChunkString.substring('data: '.length));
        
        // Should use default 'stop' as finish_reason
        expect(finalChunk.choices[0].finish_reason).toBe('stop');
        expect(finalChunk.usage).toEqual({
          prompt_tokens: 1,
          completion_tokens: 2,
          total_tokens: 3
        });
        expect(mockResponse.write).toHaveBeenCalledWith('data: [DONE]\n\n');
        expect(mockResponse.end).toHaveBeenCalled();
        done();
      });
    });
  });

  describe('Error handling', () => {
    it('should handle stream errors', (done) => {
      mockStream = new Readable({
        read() {
          this.emit('error', new Error('Stream error'));
        }
      });

      transformAnthropicStream(mockStream, mockResponse, mockModelName);

      mockStream.on('error', () => {
        expect(logger.error).toHaveBeenCalledWith(
          'Error on SuperWhisper stream during Cursor transformation',
          expect.any(Object)
        );
        expect(mockResponse.end).toHaveBeenCalled();
        done();
      });
    });

    it('should handle malformed JSON data', (done) => {
      mockStream = new Readable({
        read() {
          this.push('event: message_start\ndata: {malformed_json}\n\n');
          this.push(null);
        }
      });

      transformAnthropicStream(mockStream, mockResponse, mockModelName);

      mockStream.on('end', () => {
        expect(logger.warn).toHaveBeenCalledWith(
          'Error parsing Anthropic event data or constructing OpenAI chunk during Cursor transformation',
          expect.any(Object)
        );
        expect(mockResponse.end).toHaveBeenCalled();
        done();
      });
    });
  });

  describe('Special cases', () => {
    it('should handle ping events', (done) => {
      mockStream = new Readable({
        read() {
          this.push('event: ping\ndata: {"type": "ping"}\n\n');
          this.push(null);
        }
      });

      transformAnthropicStream(mockStream, mockResponse, mockModelName);

      mockStream.on('end', () => {
        expect(mockResponse.write).toHaveBeenCalledWith(':\n\n'); // Keep-alive comment
        expect(mockResponse.end).toHaveBeenCalled();
        done();
      });
    });

    it('should not send duplicate [DONE] events', (done) => {
      mockStream = new Readable({
        read() {
          this.push('event: message_start\ndata: {"type":"message_start","message":{"id":"msg_123","usage":{"input_tokens":1}}}\n\n');
          this.push('event: message_delta\ndata: {"type":"message_delta","delta":{"stop_reason":"end_turn"},"usage":{"output_tokens":2}}\n\n');
          this.push('event: message_stop\ndata: {"type":"message_stop"}\n\n');
          this.push(null);
        }
      });

      transformAnthropicStream(mockStream, mockResponse, mockModelName);

      mockStream.on('end', () => {
        const doneEvents = mockResponse.write.mock.calls.filter(
          call => call[0] === 'data: [DONE]\n\n'
        );
        expect(doneEvents.length).toBe(1);
        expect(mockResponse.end).toHaveBeenCalled();
        done();
      });
    });

    it('should include model name in transformed chunks', (done) => {
      mockStream = new Readable({
        read() {
          this.push('event: message_start\ndata: {"type":"message_start","message":{"id":"msg_123","usage":{"input_tokens":1}}}\n\n');
          this.push(null);
        }
      });

      transformAnthropicStream(mockStream, mockResponse, mockModelName);

      mockStream.on('end', () => {
        const firstChunk = JSON.parse(mockResponse.write.mock.calls[0][0].substring('data: '.length));
        expect(firstChunk).toHaveProperty('model', mockModelName);
        expect(mockResponse.end).toHaveBeenCalled();
        done();
      });
    });
  });
});
