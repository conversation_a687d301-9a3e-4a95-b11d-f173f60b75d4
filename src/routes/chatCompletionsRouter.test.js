const request = require('supertest');
const express = require('express');
const chatCompletionsRouter = require('./chatCompletionsRouter');
const { callSuperWhisperAPI } = require('../services/superwhisperClient');
const { transformRequestPayload } = require('../transformations/messageContentTransformer');
const { PassThrough } = require('stream');
const logger =require('../utils/logger');

// Mock dependencies
jest.mock('../services/superwhisperClient');
jest.mock('../utils/logger');
jest.mock('../transformations/anthropicSseTransformer', () => ({
    transformAnthropicStream: jest.fn((stream, res, model) => {
        stream.pipe(res); 
    }),
}));

// Mock the new transformer
jest.mock('../transformations/messageContentTransformer', () => ({
    transformRequestPayload: jest.fn((body, endpointType) => {
        if (body && body.messages) {
            if (endpointType === 'anthropic') {
                const systemMsgIndex = body.messages.findIndex(m => m.role === 'system');
                if (systemMsgIndex !== -1) {
                    const systemMsg = body.messages.splice(systemMsgIndex, 1)[0];
                    body.system = Array.isArray(systemMsg.content) ? systemMsg.content.map(c => (typeof c === 'object' && c.text) || c).join('\n') : String(systemMsg.content);
                }
            }
             if (body.system && Array.isArray(body.system)) {
                body.system = body.system.map(s => (typeof s === 'object' && s.text) || s).join('\n');
            }
            body.messages.forEach(msg => {
                if (Array.isArray(msg.content)) {
                    msg.content = msg.content.map(c => (typeof c === 'object' && c.text) || c).join("\n");
                }
            });
        }
        return body; 
    }),
}));

jest.mock('../middleware/authMiddleware', () => ({
    authenticateToken: jest.fn((req, res, next) => next()),
}));

const app = express();
app.use(express.json());
app.use('/chat/completions', (req, res, next) => {
    process.env.SUPERWHISPER_API_BASE_URL_OPENAI = process.env.SUPERWHISPER_API_BASE_URL_OPENAI || 'http://fake.openai.sw.api';
    process.env.SUPERWHISPER_API_BASE_URL_ANTHROPIC = process.env.SUPERWHISPER_API_BASE_URL_ANTHROPIC || 'http://fake.anthropic.sw.api';
    process.env.SUPERWHISPER_USER_ID = process.env.SUPERWHISPER_USER_ID || 'test-user';
    process.env.SUPERWHISPER_HEADER_X_ID = process.env.SUPERWHISPER_HEADER_X_ID || 'test-x-id';
    process.env.SUPERWHISPER_HEADER_X_LICENSE = process.env.SUPERWHISPER_HEADER_X_LICENSE || 'test-x-license';
    process.env.SUPERWHISPER_HEADER_X_SIGNATURE = process.env.SUPERWHISPER_HEADER_X_SIGNATURE || 'test-x-signature';
    next();
}, chatCompletionsRouter);

app.use((err, req, res, next) => {
  if (res.headersSent) {
    return next(err);
  }
  res.status(err.status || err.statusCode || 500).json({ error: err.message || 'Something broke!', details: err.details });
});

describe('POST /chat/completions', () => {
  let originalEnv;

  beforeEach(() => {
    originalEnv = { ...process.env };
    callSuperWhisperAPI.mockReset();
    logger.info.mockClear();
    logger.debug.mockClear();
    logger.warn.mockClear();
    logger.error.mockClear();
    require('../transformations/anthropicSseTransformer').transformAnthropicStream.mockClear();
    transformRequestPayload.mockImplementation((body, endpointType) => {
        if (body && body.messages) {
            if (endpointType === 'anthropic') {
                const systemMsgIndex = body.messages.findIndex(m => m.role === 'system');
                if (systemMsgIndex !== -1) {
                    const systemMsg = body.messages.splice(systemMsgIndex, 1)[0];
                    body.system = Array.isArray(systemMsg.content) ? systemMsg.content.map(c => (typeof c === 'object' && c.text !== undefined ? c.text : c)).join('\n') : String(systemMsg.content);
                }
            }
            if (body.system && Array.isArray(body.system)) {
                 body.system = body.system.map(s => (typeof s === 'object' && s.text !== undefined ? s.text : s)).join("\n");
            }
            body.messages.forEach(msg => {
                if (Array.isArray(msg.content)) {
                    msg.content = msg.content.map(c => (typeof c === 'object' && c.text !== undefined ? c.text : c)).join("\n");
                }
            });
        }
        return body;
    });


    process.env.SUPERWHISPER_API_BASE_URL_OPENAI = 'http://fake.openai.sw.api';
    process.env.SUPERWHISPER_API_BASE_URL_ANTHROPIC = 'http://fake.anthropic.sw.api';
  });

  afterEach(() => {
    process.env = originalEnv;
    jest.clearAllMocks();
  });

  it('should call transformRequestPayload with request body and endpoint type', async () => {
    const mockRequestBody = { model: 'gpt-4.1-2025-04-14', messages: [{ role: 'user', content: 'Hello' }] };
    callSuperWhisperAPI.mockResolvedValue({ id: "res_123", text: 'Mocked Response' });

    await request(app)
      .post('/chat/completions')
      .send(mockRequestBody);

    expect(transformRequestPayload).toHaveBeenCalledWith(
      expect.objectContaining(mockRequestBody), 
      'openai' 
    );
  });
  
  it('should use the result of transformRequestPayload for SuperWhisper API call', async () => {
    const clientRequestBody = { model: 'claude-3-5-sonnet-20241022', messages: [{role: 'system', content: 'System original'}, { role: 'user', content: 'User original' }] };
    const expectedTransformedForSW = {
        model: 'sw-claude-3-5-sonnet', 
        system: 'System original', 
        messages: [{ role: 'user', content: 'User original' }], 
        stream: true,
        user: 'test-user' 
    };
    
    transformRequestPayload.mockImplementation((bodyInput, endpointType) => {
        let systemContent = '';
        const otherMessages = [];
        if (bodyInput.messages) {
            bodyInput.messages.forEach(m => {
                if (m.role === 'system') systemContent = m.content;
                else otherMessages.push(m);
            });
        }
        return {
            ...bodyInput, 
            system: systemContent,
            messages: otherMessages
        };
    });
    callSuperWhisperAPI.mockResolvedValue({ id: "res_anthropic", text: 'Mocked Anthropic Response' });

    await request(app)
        .post('/chat/completions')
        .send(clientRequestBody);

    expect(transformRequestPayload).toHaveBeenCalledWith(expect.objectContaining(clientRequestBody), 'anthropic');
    expect(callSuperWhisperAPI).toHaveBeenCalledWith(
        expect.stringContaining('anthropic.sw.api/messages'),
        expect.objectContaining(expectedTransformedForSW),
        expect.any(Object), 
        'claude-3-5-sonnet-20241022', 
        'anthropic', 
        false 
    );
  });


  describe('Aggregated Response (stream: false or omitted)', () => {
    const testCases = [
      { name: 'stream: false', body: { model: 'gpt-4.1-2025-04-14', messages: [{ role: 'user', content: 'Hello' }], stream: false } },
      { name: 'stream omitted', body: { model: 'gpt-4.1-2025-04-14', messages: [{ role: 'user', content: 'Hello' }] } }
    ];

    testCases.forEach(tc => {
      it(`should return 200 and aggregated JSON response when ${tc.name}`, async () => {
        const mockAggregatedResponse = { id: "res_123", text: 'Mocked Aggregated SuperWhisper Response' };
        callSuperWhisperAPI.mockResolvedValue(mockAggregatedResponse);
        
        const response = await request(app)
          .post('/chat/completions')
          .send(tc.body);

        expect(response.statusCode).toBe(200);
        expect(transformRequestPayload).toHaveBeenCalledWith(expect.any(Object), 'openai');
        expect(callSuperWhisperAPI).toHaveBeenCalledWith(
          expect.any(String), 
          expect.objectContaining({ model: 'sw-gpt-4.1', messages: [{role: 'user', content: 'Hello'}], stream: true }), 
          expect.any(Object), 
          'gpt-4.1-2025-04-14', 
          'openai', 
          false 
        );
      });
    });
  });

  describe('Streaming Response (stream: true)', () => {
    // Removed (done) and used async/await with try/catch for assertions in .end()
    it('should stream response with 200 OK for Anthropic model, no special transform', async () => {
      const mockSWHeaders = new Map([['content-type', 'text/event-stream'], ['x-accel-buffering', 'yes']]);
      const mockStream = new PassThrough();
      callSuperWhisperAPI.mockResolvedValue({ rawSuperWhisperResponse: { status: 200, ok: true, headers: mockSWHeaders, body: mockStream }});

      const mockRequestBody = { 
          model: 'claude-3-5-sonnet-20241022', 
          messages: [{ role: 'user', content: 'Stream test' }], 
          stream: true 
      };

      const responsePromise = request(app)
        .post('/chat/completions')
        .send(mockRequestBody);
      
      // End the stream after the request is initiated
      process.nextTick(() => mockStream.end());

      const res = await responsePromise; // Wait for the response to complete

      expect(res.statusCode).toBe(200);
      expect(res.headers['content-type']).toMatch(/text\/event-stream/);
      expect(res.headers['x-accel-buffering']).toBe('yes');
      expect(transformRequestPayload).toHaveBeenCalledWith(expect.any(Object), 'anthropic');
      expect(callSuperWhisperAPI).toHaveBeenCalledWith(
        expect.stringContaining('anthropic.sw.api/messages'),
        expect.objectContaining({ model: 'sw-claude-3-5-sonnet', messages: [{role: 'user', content: 'Stream test'}] }),
        expect.any(Object),
        'claude-3-5-sonnet-20241022', 'anthropic', true 
      );
    });
    
    // Removed (done) and used async/await with try/catch for assertions in .end()
    it('should stream response and apply special transformation for Cursor client (Anthropic)', async () => {
        transformRequestPayload.mockImplementation((body, endpointType) => {
            if (endpointType === 'anthropic') {
                if (body.system && Array.isArray(body.system)) { 
                     body.system = body.system.map(s => (typeof s === 'object' && s.text !== undefined ? s.text : s)).join("\n");
                } else if (body.messages && body.messages.some(m => m.role === 'system')) {
                    const systemMsg = body.messages.find(m => m.role === 'system');
                    if (systemMsg) {
                        body.system = Array.isArray(systemMsg.content) ? systemMsg.content.map(c => (typeof c === 'object' && c.text !== undefined ? c.text : c)).join('\n') : String(systemMsg.content);
                        body.messages = body.messages.filter(m => m.role !== 'system');
                    }
                }
            }
            if (body.messages) {
                 body.messages.forEach(msg => {
                    if (Array.isArray(msg.content)) {
                        msg.content = msg.content.map(c => (typeof c === 'object' && c.text !== undefined ? c.text : c)).join("\n");
                    }
                });
            }
            return body;
        });

        const mockSWHeaders = new Map([['content-type', 'text/event-stream']]);
        const mockStream = new PassThrough();
        callSuperWhisperAPI.mockResolvedValue({ rawSuperWhisperResponse: { status: 200, ok: true, headers: mockSWHeaders, body: mockStream }});

        const mockRequestBody = { 
            model: 'claude-3-5-sonnet-20241022', 
            messages: [{ role: 'user', content: 'Stream test for Cursor' }], 
            stream: true,
            metadata: { user_id: '64f290b7827779ba96ee80fc' },
            system: [{ type: 'text', text: 'This is a system prompt for Cursor editor.'}] 
        };

        const responsePromise = request(app)
            .post('/chat/completions')
            .set('User-Agent', 'axios/1.2.3') 
            .send(mockRequestBody);

        process.nextTick(() => mockStream.end());
        
        const res = await responsePromise;

        expect(res.statusCode).toBe(200);
        expect(transformRequestPayload).toHaveBeenCalledWith(expect.any(Object), 'anthropic');
        expect(require('../transformations/anthropicSseTransformer').transformAnthropicStream).toHaveBeenCalledTimes(1);
        expect(logger.info).toHaveBeenCalledWith(
            expect.stringContaining('Special Anthropic Transformation Conditions Evaluation:'),
            expect.objectContaining({ allConditionsMet: true, systemPromptType: 'string' }) 
        );
    });
  });


  it('should return 400 if model is missing', async () => {
    const response = await request(app)
      .post('/chat/completions')
      .send({ messages: [{ role: 'user', content: 'Hello' }] });
    expect(response.statusCode).toBe(400);
  });

  it('should return 400 for unsupported model', async () => {
    const response = await request(app)
      .post('/chat/completions')
      .send({ model: 'unsupported-model', messages: [{ role: 'user', content: 'Hello' }] });
    expect(response.statusCode).toBe(400);
    expect(response.body.message).toContain('Unsupported model: unsupported-model');
  });

  it('should accept SuperWhisper model names directly', async () => {
    const mockAggregatedResponse = { id: "res_sw", text: 'Mocked SuperWhisper Direct Response' };
    callSuperWhisperAPI.mockResolvedValue(mockAggregatedResponse);
    
    const response = await request(app)
      .post('/chat/completions')
      .send({ model: 'sw-claude-4-sonnet', messages: [{ role: 'user', content: 'Hello' }] });

    expect(response.statusCode).toBe(200);
    expect(transformRequestPayload).toHaveBeenCalledWith(expect.any(Object), 'anthropic');
    expect(callSuperWhisperAPI).toHaveBeenCalledWith(
      expect.stringContaining('anthropic.sw.api/messages'),
      expect.objectContaining({ 
        model: 'sw-claude-4-sonnet', 
        messages: [{role: 'user', content: 'Hello'}], 
        stream: true 
      }), 
      expect.any(Object), 
      'sw-claude-4-sonnet', 
      'anthropic', 
      false 
    );
    expect(logger.info).toHaveBeenCalledWith('Direct SuperWhisper model name used: sw-claude-4-sonnet');
  });

  it('should accept both public and SuperWhisper model names for the same model', async () => {
    const mockAggregatedResponse = { id: "res_both", text: 'Mocked Response' };
    callSuperWhisperAPI.mockResolvedValue(mockAggregatedResponse);
    
    // Test public-facing model name
    const response1 = await request(app)
      .post('/chat/completions')
      .send({ model: 'claude-sonnet-4-20250514', messages: [{ role: 'user', content: 'Hello' }] });

    expect(response1.statusCode).toBe(200);
    
    // Test SuperWhisper model name
    const response2 = await request(app)
      .post('/chat/completions')
      .send({ model: 'sw-claude-4-sonnet', messages: [{ role: 'user', content: 'Hello' }] });

    expect(response2.statusCode).toBe(200);
    
    // Both should result in the same SuperWhisper model being called
    expect(callSuperWhisperAPI).toHaveBeenCalledTimes(2);
    expect(callSuperWhisperAPI).toHaveBeenNthCalledWith(1,
      expect.any(String),
      expect.objectContaining({ model: 'sw-claude-4-sonnet' }),
      expect.any(Object),
      'claude-sonnet-4-20250514',
      'anthropic',
      false
    );
    expect(callSuperWhisperAPI).toHaveBeenNthCalledWith(2,
      expect.any(String),
      expect.objectContaining({ model: 'sw-claude-4-sonnet' }),
      expect.any(Object),
      'sw-claude-4-sonnet',
      'anthropic',
      false
    );
  });
  
  // Increased timeout for this specific test due to complex mocks and stream handling
  it('should handle the example problematic request structure correctly', async () => {
    const problematicRequest = {
      "model": "claude-3-5-sonnet-20241022",
      "messages": [
        { "role": "system", "content": "You are Cline..." },
        { "role": "user", "content": [ { "type": "text", "text": "<task>explain</task>" }, { "type": "text", "text": "<environment>details</environment>" }] }
      ],
      "stream": true,
    };

    transformRequestPayload.mockImplementation((body, endpointType) => {
        let systemContent = '';
        const otherMessages = [];
        if (body.messages) {
            body.messages.forEach(m => {
                if (m.role === 'system') {
                     systemContent = Array.isArray(m.content) ? m.content.map(c => (typeof c === 'object' && c.text !== undefined ? c.text : c)).join('\n') : String(m.content);
                } else {
                    let currentContent = m.content;
                    if(Array.isArray(currentContent)) {
                        currentContent = currentContent.map(c => (typeof c === 'object' && c.text !== undefined ? c.text : c)).join('\n');
                    }
                    otherMessages.push({...m, content: currentContent});
                }
            });
        }
        const newBody = {...body, messages: otherMessages};
        if (systemContent && endpointType === 'anthropic') {
            newBody.system = systemContent;
        } else if (systemContent && endpointType === 'openai') {
            newBody.messages.unshift({role: 'system', content: systemContent});
        }
        delete newBody.messages; // Simulate the transformation accurately for callSuperWhisperAPI check
        newBody.messages = otherMessages; // Re-assign the filtered messages

        return newBody;
    });

    const mockSWHeaders = new Map([['content-type', 'text/event-stream']]);
    const mockStream = new PassThrough(); 
    callSuperWhisperAPI.mockResolvedValue({ rawSuperWhisperResponse: { status: 200, ok: true, headers: mockSWHeaders, body: mockStream }});

    const resPromise = request(app)
        .post('/chat/completions')
        .send(problematicRequest);
    
    // Allow the event loop to process, then end the stream
    // This helps ensure the server-side logic related to the stream has a chance to run.
    process.nextTick(() => {
        mockStream.write('data: chunk1\n\n'); // Simulate some data
        mockStream.end();
    });

    const res = await resPromise; // Wait for the client's request to complete

    expect(res.statusCode).toBe(200); 

    expect(transformRequestPayload).toHaveBeenCalledWith(expect.objectContaining(problematicRequest), 'anthropic');
    
    expect(callSuperWhisperAPI).toHaveBeenCalledWith(
        expect.stringContaining('anthropic.sw.api/messages'),
        expect.objectContaining({
            model: 'sw-claude-3-5-sonnet',
            system: "You are Cline...", 
            messages: [ 
                { role: 'user', content: "<task>explain</task>\n<environment>details</environment>" }
            ],
            stream: true,
        }),
        expect.any(Object), 
        "claude-3-5-sonnet-20241022", 
        'anthropic', 
        true 
    );
  }, 10000); // Increased timeout to 10 seconds


  describe('Dry Run Mode', () => {
    beforeEach(() => {
      process.env.DRY_RUN = 'true';
    });

    it('should return 200 with dry_run_success message and not call SuperWhisper API', async () => {
      const mockRequestBody = { model: 'claude-3-5-sonnet-20241022', messages: [{ role: 'user', content: 'Test dry run' }] };
      
      const response = await request(app) 
        .post('/chat/completions')
        .send(mockRequestBody);

      expect(response.statusCode).toBe(200); 
      expect(transformRequestPayload).toHaveBeenCalledWith(expect.any(Object), 'anthropic');
      expect(callSuperWhisperAPI).not.toHaveBeenCalled();
      expect(logger.info).toHaveBeenCalledWith('[DRY_RUN] Mode enabled. Request will be logged but not sent to SuperWhisper.');
    });
  });
});

describe('OPTIONS /chat/completions', () => {
    beforeEach(() => {
        Object.values(logger).forEach(mockFn => {
            if (jest.isMockFunction(mockFn)) mockFn.mockClear();
        });
         transformRequestPayload.mockClear();
    });
    it('should return 204 No Content for OPTIONS request', async () => {
        const response = await request(app).options('/chat/completions');
        expect(response.statusCode).toBe(204);
        expect(logger.info).toHaveBeenCalledWith('OPTIONS request to /chat/completions');
        expect(transformRequestPayload).not.toHaveBeenCalled(); 
    });
});
