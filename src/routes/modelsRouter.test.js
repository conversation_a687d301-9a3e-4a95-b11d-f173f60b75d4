const request = require('supertest');
const express = require('express');
const modelsDataRaw = require('./modelsRouter').modelsData; // Import the actual data for comparison
const modelsRouter = require('./modelsRouter');
const logger = require('../utils/logger');

jest.mock('../utils/logger'); // Auto-mock logger

const app = express();
app.use(express.json());
app.use('/models', modelsRouter);

describe('/models endpoint', () => {
    beforeEach(() => {
        // Clear all logger mocks
        Object.values(logger).forEach(mockFn => {
            if (jest.isMockFunction(mockFn)) {
                mockFn.mockClear();
            }
        });
    });

  it('GET /models should return the list of models', async () => {
    const response = await request(app).get('/models');
    expect(response.statusCode).toBe(200);
    expect(response.body).toHaveProperty('object', 'list');
    expect(response.body.data).toBeInstanceOf(Array); // Based on current modelsData
    expect(response.body.data.length).toBe(10); // Updated to 10 (5 public + 5 SuperWhisper models)
    expect(response.body.data[0]).toHaveProperty('id');
    expect(response.body.data[0]).toHaveProperty('owned_by', 'system');
    expect(logger.info).toHaveBeenCalledTimes(2); // Incoming request and outgoing response
    expect(logger.info).toHaveBeenCalledWith('Incoming request to /models', expect.any(Object));
    expect(logger.info).toHaveBeenCalledWith('Outgoing response from /models', expect.any(Object));
  });

  it('OPTIONS /models should return 200 OK with models data because preflightContinue is true', async () => {
    const response = await request(app).options('/models');
    // Now that preflightContinue: true, our custom OPTIONS handler in modelsRouter.js is hit.
    // That handler calls res.json(modelsData), which results in a 200 OK.
    expect(response.statusCode).toBe(200);
    expect(response.body).toEqual(modelsDataRaw); // Check if the body matches the expected data

    // The modelsRouter.js OPTIONS handler logs "Incoming request to /models"
    // and "Outgoing response from /models"
    expect(logger.info).toHaveBeenCalledTimes(2);
    expect(logger.info).toHaveBeenCalledWith('Incoming request to /models', expect.any(Object));
    expect(logger.info).toHaveBeenCalledWith('Outgoing response from /models', expect.any(Object));
  });
});
