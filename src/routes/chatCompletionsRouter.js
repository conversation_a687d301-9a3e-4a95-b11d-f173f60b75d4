const express = require('express');
const { authenticateToken } = require('../middleware/authMiddleware');
const { transformRequestPayload } = require('../transformations/messageContentTransformer');
const { callSuperWhisperAPI } = require('../services/superwhisperClient');
const { transformAnthropicStream } = require('../transformations/anthropicSseTransformer');
const { transformClaudeCodeStream } = require('../transformations/claudeCodeStreamTransformer');
const logger = require('../utils/logger');

const router = express.Router();

// ... (modelMapping, modelEndpointType, and helper functions remain unchanged) ...
const modelMapping = {
  'claude-3-5-haiku-20241022': 'sw-claude-3-5-haiku',
  'claude-3-5-haiku-latest': 'sw-claude-3-5-haiku',
  'claude-3-5-sonnet-20241022': 'sw-claude-3-5-sonnet',
  'claude-3-5-sonnet-latest': 'sw-claude-3-5-sonnet',
  'claude-3-7-sonnet-20250219': 'sw-claude-3-7-sonnet',
  'claude-3-7-sonnet-latest': 'sw-claude-3-7-sonnet',
  'claude-sonnet-4-20250514': 'sw-claude-4-sonnet',
  'gpt-4.1-2025-04-14': 'sw-gpt-4.1',
};
const modelEndpointType = {
  'sw-claude-3-5-haiku': 'anthropic',
  'sw-claude-3-5-sonnet': 'anthropic',
  'sw-claude-3-7-sonnet': 'anthropic',
  'sw-claude-4-sonnet': 'anthropic',
  'sw-gpt-4.1': 'openai',
};

function checkSystemForCursor(systemPrompt) { 
  if (typeof systemPrompt === 'string') {
    const result = systemPrompt.toLowerCase().includes('cursor');
    logger.debug(`System prompt condition for "Cursor" (string input): Found "Cursor"? ${result}. Content sample: "${systemPrompt.substring(0, 70)}${systemPrompt.length > 70 ? "..." : ""}"`);
    return result;
  }

  if (systemPrompt && Array.isArray(systemPrompt) && systemPrompt.length > 0) {
    let foundTextPropertyInSystem = false;
    for (const message of systemPrompt) {
      if (message && typeof message.text === 'string') {
        foundTextPropertyInSystem = true;
        if (message.text.toLowerCase().includes('cursor')) {
          logger.debug('System prompt condition for "Cursor" (array input): Found "Cursor". Condition is true.');
          return true;
        }
      }
    }
    if (foundTextPropertyInSystem) {
      logger.debug('System prompt condition for "Cursor" (array input): Text properties found, but "Cursor" was not. Condition is false.');
      return false;
    }
    logger.debug('System prompt condition for "Cursor" (array input): Defaulting to true (no text properties found in system messages array or empty array).');
    return true; 
  }
  logger.debug('System prompt condition for "Cursor": Defaulting to true (system prompt is missing, empty, or not a string/supported array).');
  return true; 
}

function checkSystemForCline(systemPrompt) {
  if (typeof systemPrompt === 'string') {
    const result = systemPrompt.toLowerCase().includes('cline');
    logger.debug(`System prompt condition for "Cline" (string input): Found "Cline"? ${result}. Content sample: "${systemPrompt.substring(0, 70)}${systemPrompt.length > 70 ? "..." : ""}"`);
    return result;
  }

  if (systemPrompt && Array.isArray(systemPrompt) && systemPrompt.length > 0) {
    let foundTextPropertyInSystem = false;
    for (const message of systemPrompt) {
      if (message && typeof message.text === 'string') {
        foundTextPropertyInSystem = true;
        if (message.text.toLowerCase().includes('cline')) {
          logger.debug('System prompt condition for "Cline" (array input): Found "Cline". Condition is true.');
          return true;
        }
      }
    }
    if (foundTextPropertyInSystem) {
      logger.debug('System prompt condition for "Cline" (array input): Text properties found, but "Cline" was not. Condition is false.');
      return false;
    }
    logger.debug('System prompt condition for "Cline" (array input): Defaulting to true (no text properties found in system messages array or empty array).');
    return true; 
  }
  logger.debug('System prompt condition for "Cline": Defaulting to true (system prompt is missing, empty, or not a string/supported array).');
  return true;
}

function shouldApplySpecialAnthropicTransformation(req) {
  const clientUserAgent = req.headers['user-agent'] || '';
  const clientRequest = req.body;

  const isCursorUserAgent = clientUserAgent.toLowerCase().includes('axios');
  const hasCursorUserId = clientRequest.metadata && clientRequest.metadata.user_id === '64f290b7827779ba96ee80fc';
  const isCursorClient = isCursorUserAgent && hasCursorUserId;

  const isClineUserAgent = /[\w\$\-\_\+\&\@\#\!]+\/JS \d+\.\d{2}\.\d+/.test(clientUserAgent);
  const hasClineInSystem = checkSystemForCline(clientRequest.system);
  const isClineClient = isClineUserAgent && hasClineInSystem;

  const isMstyClient = clientUserAgent.toLowerCase().includes('openai/js');
  const isGitHubCopilotChatClient = clientUserAgent.toLowerCase().includes('githubcopilotchat');
  const allConditionsMet = isCursorClient || isClineClient || isMstyClient || isGitHubCopilotChatClient;

  let systemContentSample = "N/A";
  if (typeof clientRequest.system === 'string') {
    systemContentSample = clientRequest.system.substring(0, 70) + (clientRequest.system.length > 70 ? "..." : "");
  } else if (Array.isArray(clientRequest.system) && clientRequest.system[0] && typeof clientRequest.system[0].text === 'string') {
    systemContentSample = clientRequest.system[0].text.substring(0, 70) + (clientRequest.system[0].text.length > 70 ? "..." : "");
  }

  logger.info('Special Anthropic Transformation Conditions Evaluation:', {
    userAgent: clientUserAgent,
    isCursorUserAgent, isClineUserAgent, isMstyClient, isGitHubCopilotChatClient,
    userId: clientRequest.metadata ? clientRequest.metadata.user_id : 'N/A',
    hasCursorUserId, systemContentSample: systemContentSample, systemPromptType: typeof clientRequest.system,
    hasClineInSystem, isCursorClient, isClineClient, allConditionsMet
  });
  return allConditionsMet;
}

function isClaudeCodeClient(req) {
  const clientUserAgent = req.headers['user-agent'] || '';
  const clientRequest = req.body;
  
  const isClaudeCodeUserAgent = clientUserAgent.toLowerCase().includes('claude-code') || 
                                clientUserAgent.toLowerCase().includes('claudecode') ||
                                clientUserAgent.toLowerCase().includes('claude-cli');
  
  let hasClaudeCodeInSystem = false;
  if (typeof clientRequest.system === 'string') {
    hasClaudeCodeInSystem = clientRequest.system.toLowerCase().includes('claude code') ||
                           clientRequest.system.toLowerCase().includes('claude-code');
  } else if (Array.isArray(clientRequest.system)) {
    hasClaudeCodeInSystem = clientRequest.system.some(msg => 
      msg && typeof msg.text === 'string' && 
      (msg.text.toLowerCase().includes('claude code') || msg.text.toLowerCase().includes('claude-code'))
    );
  }
  
  const isClaudeCode = isClaudeCodeUserAgent || hasClaudeCodeInSystem;
  
  logger.debug('Claude Code client detection:', {
    userAgent: clientUserAgent, isClaudeCodeUserAgent, hasClaudeCodeInSystem, isClaudeCode
  });
  
  return isClaudeCode;
}

function isZedClient(req) {
  const clientUserAgent = req.headers['user-agent'] || '';
  const isZedUserAgent = clientUserAgent.toLowerCase().includes('zed');
  
  logger.debug('Zed client detection:', {
    userAgent: clientUserAgent, isZedUserAgent
  });
  
  return isZedUserAgent;
}


router.post('/', authenticateToken, async (req, res, next) => {
  const requestTimestamp = new Date().toISOString();
  if (process.env.DRY_RUN === 'true') {
    logger.info('[DRY_RUN] Mode enabled. Request will be logged but not sent to SuperWhisper.');
  }
  let clientRequest = req.body; 
  // Make a copy for logging in case of errors later
  const originalClientRequestForLog = JSON.parse(JSON.stringify(req.body)); 
  const clientRequestedModel = clientRequest.model;

  // --- THIS IS THE KEY CHANGE ---
  // The 'requestClient' debug logging is now handled by the new `debugLoggingMiddleware`
  // so we REMOVE the call from here.
  // logger.debugData('requestClient', ...); // <-- This line is deleted.

  const clientUserAgent = req.headers['user-agent'] || '';

  logger.info('Incoming request to /chat/completions', {
    type: 'CLIENT_REQUEST',
    timestamp: requestTimestamp,
    method: req.method,
    url: req.originalUrl,
    headers: {
      'User-Agent': clientUserAgent,
      'Content-Type': req.headers['content-type'],
      'Authorization': req.headers.authorization ? 'Bearer [REDACTED]' : undefined,
    },
    // The body logged here might be transformed if it came via /v1/messages
    body: originalClientRequestForLog, 
  });

  try {
    if (!clientRequest || !clientRequest.model) {
      logger.warn('Bad Request: Missing model in request body', { body: clientRequest });
      return res.status(400).json({ error: "Bad Request", message: "Missing 'model' field in request body." });
    }
    let superWhisperModelName;
    if (clientRequestedModel.startsWith('sw-') && modelEndpointType[clientRequestedModel]) {
      superWhisperModelName = clientRequestedModel;
      logger.info(`Direct SuperWhisper model name used: ${clientRequestedModel}`);
    } else {
      superWhisperModelName = modelMapping[clientRequestedModel];
      if (!superWhisperModelName) {
        logger.warn(`Bad Request: Unsupported model requested: ${clientRequestedModel}`);
        return res.status(400).json({ error: "Bad Request", message: `Unsupported model: ${clientRequestedModel}` });
      }
    }
    
    const endpointType = modelEndpointType[superWhisperModelName];

    try {
      // This transformation is what we needed to log BEFORE.
      clientRequest = transformRequestPayload(clientRequest, endpointType, req); 
      logger.info('Request body after transformations for SuperWhisper.', {
          transformedBodyPreview: { 
              model: clientRequest.model,
              system: clientRequest.system ? (String(clientRequest.system).substring(0, 70) + "...") : undefined,
              messagesCount: clientRequest.messages ? clientRequest.messages.length : 0,
              firstMessageContentSample: clientRequest.messages && clientRequest.messages[0] && clientRequest.messages[0].content ? (String(clientRequest.messages[0].content).substring(0,70) + "...") : "N/A"
          }
      });
    } catch (transformError) {
      logger.error('Error during request payload transformation', { error: transformError.message, originalBody: originalClientRequestForLog, stack: transformError.stack });
      return res.status(400).json({ error: "Bad Request", message: "Invalid request format for transformation." });
    }
    
    // ... (The rest of the file remains the same) ...
    const targetSuperWhisperUrl = endpointType === 'anthropic'
      ? `${process.env.SUPERWHISPER_API_BASE_URL_ANTHROPIC}/messages`
      : `${process.env.SUPERWHISPER_API_BASE_URL_OPENAI}/chat/completions`;

    if (clientRequest.hasOwnProperty('stream') && clientRequest.stream !== true) {
      logger.warn('Client sent stream parameter, overriding to true for SuperWhisper.', { clientStreamValue: clientRequest.stream });
    }
    const superWhisperRequestBody = {
      ...clientRequest, 
      model: superWhisperModelName,
      user: process.env.SUPERWHISPER_USER_ID,
      stream: true, 
    };

    const superWhisperHeaders = {
      'Content-Type': 'application/json',
      'x-id': process.env.SUPERWHISPER_HEADER_X_ID,
      'x-license': process.env.SUPERWHISPER_HEADER_X_LICENSE,
      'x-signature': process.env.SUPERWHISPER_HEADER_X_SIGNATURE,
      'user-agent': process.env.SUPERWHISPER_REQUEST_USER_AGENT || 'superwhisper/2.0.1 (com.superduper.superwhisper; build:1.45.14; macOS 15.2.0) Alamofire/5.8.0',
      'accept': '*/*',
      'accept-language': 'en-NZ;q=1.0',
      'accept-encoding': 'br;q=1.0, gzip;q=0.9, deflate;q=0.8',
    };

    logger.info('Outgoing request to SuperWhisper API', {
      type: 'SUPERWHISPER_REQUEST',
      timestamp: new Date().toISOString(),
      method: 'POST',
      url: targetSuperWhisperUrl,
      headers: superWhisperHeaders, 
      body: superWhisperRequestBody,
    });

    if (process.env.DRY_RUN === 'true') {
      logger.info('[DRY_RUN] Prepared SuperWhisper Request (not sent):', {
        url: targetSuperWhisperUrl,
        body: superWhisperRequestBody,
      });
      return res.status(200).json({ status: "dry_run_success", message: "Request logged, not sent to SuperWhisper API." });
    }

    const clientWantsStream = originalClientRequestForLog.stream === true; 

    const apiResult = await callSuperWhisperAPI(
      targetSuperWhisperUrl,
      superWhisperRequestBody,
      superWhisperHeaders,
      clientRequestedModel, 
      endpointType,
      clientWantsStream 
    );

    if (clientWantsStream) {
      const { rawSuperWhisperResponse } = apiResult;
      
      res.status(rawSuperWhisperResponse.status);
      
      logger.instrumentResponseForDebug(res, 'responseProxy', clientRequestedModel);

      if (rawSuperWhisperResponse.status !== 200) {
        const upstreamContentType = rawSuperWhisperResponse.headers.get('content-type');
        if (upstreamContentType) res.setHeader('Content-Type', upstreamContentType);
        const upstreamCacheControl = rawSuperWhisperResponse.headers.get('cache-control');
        if (upstreamCacheControl) res.setHeader('Cache-Control', upstreamCacheControl);
        
        logger.warn(`SuperWhisper returned error status ${rawSuperWhisperResponse.status} for stream. Piping error stream directly.`);
        rawSuperWhisperResponse.body.pipe(res);
        rawSuperWhisperResponse.body.on('error', (streamError) => logger.error('Error on SuperWhisper error stream pipe to client', { message: streamError.message }));
        rawSuperWhisperResponse.body.on('end', () => logger.info('SuperWhisper error stream pipe to client ended.'));
        return;
      }
      
      res.setHeader('Content-Type', 'text/event-stream');
      res.setHeader('Cache-Control', 'no-cache');
      res.setHeader('Connection', 'keep-alive');
      res.setHeader('X-Accel-Buffering', rawSuperWhisperResponse.headers.get('x-accel-buffering') || 'no');
      
      if (endpointType === 'anthropic' && shouldApplySpecialAnthropicTransformation(req)) {
        transformAnthropicStream(rawSuperWhisperResponse.body, res, clientRequestedModel);
      } else if (endpointType === 'anthropic' && (isClaudeCodeClient(req) || isZedClient(req))) {
        const clientType = isClaudeCodeClient(req) ? 'Claude-CLI' : 'Zed';
        logger.info(`${clientType} client detected - applying Claude Code tool transformation for streaming response.`);
        transformClaudeCodeStream(rawSuperWhisperResponse.body, res);
      } else {
        if (endpointType === 'anthropic') {
          logger.info('Piping Anthropic SSE directly from SuperWhisper to client (no special transformations).');
        } else if (endpointType === 'openai') {
          logger.info('Piping OpenAI SSE directly from SuperWhisper to client.');
        }
        rawSuperWhisperResponse.body.pipe(res);
        rawSuperWhisperResponse.body.on('error', (streamError) => {
          logger.error('Error during direct pipe of SuperWhisper stream', { endpointType, message: streamError.message });
        });
        rawSuperWhisperResponse.body.on('end', () => {
          logger.info('Direct pipe of SuperWhisper stream ended.', { endpointType });
        });
      }
      return;
    }

    logger.debugData('responseProxy', {
      timestamp: new Date().toISOString(),
      statusCode: 200,
      body: apiResult,
    }, clientRequestedModel);
    
    const clientResponseLog = {
      type: 'CLIENT_RESPONSE',
      timestamp: new Date().toISOString(),
      statusCode: 200,
      body: apiResult,
    };
    logger.info('Outgoing aggregated response to client', clientResponseLog);
    res.status(200).json(apiResult);

  } catch (error) {
    const errorLog = {
      type: 'PROXY_ERROR',
      timestamp: new Date().toISOString(),
      message: error.message,
      stack: error.stack,
      context: 'chatCompletionsRouter.post',
      originalClientRequestBody: originalClientRequestForLog,
      userAgent: req.headers['user-agent'] || 'unknown',
    };
    if (error.response) { 
      errorLog.superwhisperStatus = error.response.status;
      errorLog.superwhisperResponse = error.superwhisperResponseData || 'Could not parse SuperWhisper error response';
    } else if (error.isSuperWhisperError) { 
      errorLog.superwhisperStatus = error.status;
      errorLog.superwhisperResponse = error.superwhisperResponseData || error.message;
    }
    logger.error('Error processing /chat/completions request', errorLog);
    error.logged = true; 
    next(error);
  }
});

router.options('/', (req, res) => {
  logger.info('OPTIONS request to /chat/completions');
  res.status(204).end();
});

module.exports = router;