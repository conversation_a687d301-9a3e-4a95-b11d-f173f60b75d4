const express = require('express');
const { modelsData } = require('../config/models');
const logger = require('../utils/logger');

const router = express.Router();

router.get('/', (req, res) => {
  const logEntry = {
    type: 'CLIENT_REQUEST',
    method: req.method,
    url: req.originalUrl,
    headers: {
      'User-Agent': req.headers['user-agent'],
      'Content-Type': req.headers['content-type'],
    },
    body: req.body,
  };
  logger.info('Incoming request to /models', logEntry);

  const responseLogEntry = {
    type: 'CLIENT_RESPONSE',
    statusCode: 200,
    body: modelsData,
  };
  logger.info('Outgoing response from /models', responseLogEntry);

  res.json(modelsData);
});

router.options('/', (req, res) => {
  const logEntry = {
    type: 'CLIENT_REQUEST',
    method: req.method,
    url: req.originalUrl,
    headers: {
      'User-Agent': req.headers['user-agent'],
      'Content-Type': req.headers['content-type'],
    },
    body: req.body,
  };
  logger.info('Incoming OPTIONS request to /models', logEntry);
  
  res.status(204).send();
});

module.exports = router;