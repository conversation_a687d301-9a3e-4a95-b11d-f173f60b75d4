const fetch = require('node-fetch');
const { Readable } = require('stream');
const logger = require('../utils/logger');
const { formatResponse } = require('../utils/responseFormatter');

// --- START: New Intelligent Throttling Logic ---

// A promise queue to serialize access to the throttle logic, preventing race conditions.
let requestQueue = Promise.resolve();

// Timestamp (in ms) of when the next request is allowed to be dispatched.
// Initialized to 0 so the very first request is never delayed.
let nextRequestAllowedAt = 0;

/**
 * Returns a random spacing delay in milliseconds.
 * @returns {number} A random integer between 700 and 1600.
 */
function getSpacingDelay() {
  const minDelay = 700;  // 0.7 seconds
  const maxDelay = 1600; // 1.6 seconds
  return Math.floor(Math.random() * (maxDelay - minDelay + 1)) + minDelay;
}

// --- END: New Intelligent Throttling Logic ---


async function callSuperWhisperAPI(targetUrl, swRequestBody, swRequestHeaders, clientModelName, endpointType, clientWantsStream = false) {
  const executionPromise = requestQueue.then(async () => {
    // This inner block is now serialized, ensuring our timestamp logic is safe.

    const now = Date.now();

    // 1. Calculate how long we need to wait, if at all.
    const waitTime = Math.max(0, nextRequestAllowedAt - now);

    // 2. IMPORTANT: Reserve the slot for the *next* request immediately.
    // The next request will be allowed after this one is dispatched + its random spacing.
    // `now + waitTime` is the time this request will actually be sent.
    nextRequestAllowedAt = now + waitTime + getSpacingDelay();

    if (waitTime > 0) {
      logger.info(`[Throttling] Requests are rapid. Waiting for ${waitTime}ms to enforce spacing.`);
      await new Promise(resolve => setTimeout(resolve, waitTime));
    }

    // 3. Execute the original API call logic.
    const requestStartTime = Date.now();
    let superwhisperNodeFetchResponse;

    try {
      logger.debugData('requestProxy', {
        url: targetUrl,
        method: 'POST',
        headers: swRequestHeaders,
        body: swRequestBody,
      }, clientModelName);

      superwhisperNodeFetchResponse = await fetch(targetUrl, {
        method: 'POST',
        headers: swRequestHeaders,
        body: JSON.stringify(swRequestBody),
      });

      const responseMetadataForDebug = {
          status: superwhisperNodeFetchResponse.status,
          statusText: superwhisperNodeFetchResponse.statusText,
          headers: superwhisperNodeFetchResponse.headers.raw(),
      };
      const instrumentedBodyStream = logger.debugTee('responseSW', responseMetadataForDebug, clientModelName);
      superwhisperNodeFetchResponse.body.pipe(instrumentedBodyStream);


      if (clientWantsStream) {
        logger.info('SuperWhisper API raw response received (for client streaming)', {
            status: superwhisperNodeFetchResponse.status,
            url: targetUrl,
        });
        const instrumentedResponse = {
          status: superwhisperNodeFetchResponse.status,
          statusText: superwhisperNodeFetchResponse.statusText,
          headers: superwhisperNodeFetchResponse.headers,
          body: instrumentedBodyStream,
        };
        return { rawSuperWhisperResponse: instrumentedResponse };
      }

      const superwhisperResponseStatusForLog = superwhisperNodeFetchResponse.status;
      const superwhisperResponseHeadersForLog = superwhisperNodeFetchResponse.headers.raw();
 
      logger.info('SuperWhisper API raw response status', {
          status: superwhisperResponseStatusForLog,
          url: targetUrl,
          headers: superwhisperResponseHeadersForLog
      });

      if (!superwhisperNodeFetchResponse.ok) {
        let errorData = 'Failed to read error response from SuperWhisper';
        try {
          errorData = await instrumentedBodyStream.text();
        } catch (e) {
          logger.error('Could not parse SuperWhisper error response body', { parseError: e.message });
        }
        logger.error('SuperWhisper API Error', {
          url: targetUrl,
          status: superwhisperNodeFetchResponse.status,
          statusText: superwhisperNodeFetchResponse.statusText,
          responseBody: errorData,
        });
        const err = new Error(`SuperWhisper API request failed with status ${superwhisperNodeFetchResponse.status}: ${superwhisperNodeFetchResponse.statusText}`);
        err.status = superwhisperNodeFetchResponse.status;
        err.isSuperWhisperError = true;
        err.superwhisperResponseData = errorData;
        throw err;
      }

      const contentType = superwhisperNodeFetchResponse.headers.get('content-type');
      if (!contentType || !contentType.includes('text/event-stream')) {
        let responseText = 'Could not read non-stream response body';
        try {
          responseText = await instrumentedBodyStream.text();
        } catch(e) {
          logger.error('Could not parse SuperWhisper non-stream response body', { parseError: e.message });
        }
        logger.error('SuperWhisper API did not return a stream. Unexpected Content-Type.', {
          url: targetUrl,
          status: superwhisperNodeFetchResponse.status,
          contentType: contentType,
          body: responseText
        });
        throw new Error('SuperWhisper API did not return a stream as expected.');
      }

      const accumulatedStreamData = await accumulateStream(instrumentedBodyStream, endpointType, clientModelName);
      const processingTime = Date.now() - requestStartTime;
      logger.info(`SuperWhisper stream processed in ${processingTime}ms.`);

      return formatResponse(accumulatedStreamData, clientModelName, endpointType);

    } catch (error) {
      logger.error('Error in callSuperWhisperAPI', {
        message: error.message,
        stack: error.stack,
        targetUrl,
        superwhisperStatus: superwhisperNodeFetchResponse ? superwhisperNodeFetchResponse.status : null,
      });

      if (superwhisperNodeFetchResponse && !error.status) {
          error.status = superwhisperNodeFetchResponse.status;
      }
      if (superwhisperNodeFetchResponse && !error.isSuperWhisperError && superwhisperNodeFetchResponse.status >= 400) {
          error.isSuperWhisperError = true;
      }
      throw error;
    }
  });

  requestQueue = executionPromise.catch(() => {});

  return executionPromise;
}

// ... the rest of the file (accumulateStream, handleAnthropicEvent, etc.) remains unchanged ...

async function accumulateStream(readableStream, endpointType, clientModelName) {
  const accumulated = {
    id: null,
    model: clientModelName,
    fullText: '',
    stopReason: null,
    stopSequence: null,
    inputTokens: 0,
    outputTokens: 0,
    chunks: [],
    finishReasonOpenAI: null,
    events: []
  };

  let eventCount = 0;
  let deltaContentCount = 0;

  return new Promise((resolve, reject) => {
    readableStream.on('readable', () => {
      let chunk;
      while (null !== (chunk = readableStream.read())) {
        const lines = chunk.toString('utf8').split('\n\n');
        for (const line of lines) {
          if (line.startsWith('data: ')) {
            const jsonData = line.substring('data: '.length).trim();
            if (jsonData === '[DONE]') {
              logger.info('Stream [DONE] message received from SuperWhisper.');
              accumulated.events.push({ type: '[DONE]' });
              continue;
            }
            try {
              const event = JSON.parse(jsonData);
              eventCount++;
              accumulated.events.push({ type: event.type || (event.choices ? 'openai_chunk' : 'unknown_event'), id: event.id });


              if (endpointType === 'anthropic') {
                handleAnthropicEvent(event, accumulated);
                if (event.type === 'content_block_delta') deltaContentCount++;
              } else if (endpointType === 'openai') {
                handleOpenAIEvent(event, accumulated);
                if (event.choices && event.choices[0].delta && event.choices[0].delta.content) deltaContentCount++;
              }

            } catch (e) {
              logger.warn('Failed to parse JSON from stream chunk', { chunk: jsonData, error: e.message });
            }
          }
        }
      }
    });

    readableStream.on('end', () => {
      logger.info(`SuperWhisper stream ended. Total events: ${eventCount}, Delta content events: ${deltaContentCount}.`);
      logger.info('Final accumulated full text (first 100 chars):', { textStart: accumulated.fullText.substring(0, 100) });
      if (endpointType === 'anthropic' && !accumulated.stopReason) {
        logger.warn('Anthropic stream ended but stop_reason was not captured from a message_stop or message_delta event.');
      }
      if (endpointType === 'openai' && !accumulated.finishReasonOpenAI) {
        logger.warn('OpenAI stream ended but finish_reason was not captured from a chunk.');
      }
      resolve(accumulated);
    });

    readableStream.on('error', (err) => {
      logger.error('Error reading from SuperWhisper stream', { error: err.message, stack: err.stack });
      reject(err);
    });
  });
}

function handleAnthropicEvent(event, acc) {
  if (event.type === 'message_start') {
    logger.info('SuperWhisper Event: message_start', { id: event.message?.id, model: event.message?.model, input_tokens: event.message?.usage?.input_tokens });
    if (event.message) {
      if (event.message.id) acc.id = event.message.id;
      if (event.message.model) acc.model = event.message.model;
      if (event.message.usage && event.message.usage.input_tokens !== undefined) {
        acc.inputTokens = event.message.usage.input_tokens;
      }
    }
  } else if (event.type === 'content_block_start') {
    logger.info('SuperWhisper Event: content_block_start', { index: event.index });
  } else if (event.type === 'content_block_delta') {
    if (event.delta && event.delta.type === 'text_delta' && event.delta.text) {
      acc.fullText += event.delta.text;
    }
  } else if (event.type === 'content_block_stop') {
    logger.info('SuperWhisper Event: content_block_stop', { index: event.index });
  } else if (event.type === 'message_delta') {
    logger.info('SuperWhisper Event: message_delta', { stop_reason: event.delta?.stop_reason, stop_sequence: event.delta?.stop_sequence, output_tokens: event.usage?.output_tokens });
    if (event.delta) {
      if (event.delta.stop_reason) acc.stopReason = event.delta.stop_reason;
      if (event.delta.stop_sequence) acc.stopSequence = event.delta.stop_sequence;
    }
    if (event.usage && event.usage.output_tokens !== undefined) {
      acc.outputTokens = event.usage.output_tokens;
    }
  } else if (event.type === 'message_stop') {
    logger.info('SuperWhisper Event: message_stop');
  } else if (event.type === 'ping') {
    logger.debug('SuperWhisper Event: ping');
  } else if (event.type === 'error') {
    logger.error('SuperWhisper Stream Error Event:', { errorType: event.error?.type, errorMessage: event.error?.message });
  }
}

function handleOpenAIEvent(event, acc) {
  acc.chunks.push(event);

  if (event.id && !acc.id) acc.id = event.id;
  if (event.model && acc.model !== event.model) {
      if (acc.model === clientModelName) {
          acc.model = event.model;
          logger.info(`SuperWhisper actual model for OpenAI stream: ${event.model}`);
      }
  }

  if (event.choices && event.choices.length > 0) {
    const choice = event.choices[0];
    if (choice.delta && choice.delta.content) {
      acc.fullText += choice.delta.content;
    }
    if (choice.finish_reason) {
      acc.finishReasonOpenAI = choice.finish_reason;
      logger.info('SuperWhisper OpenAI finish_reason received:', { finish_reason: acc.finishReasonOpenAI });
    }
  }
}

module.exports = { callSuperWhisperAPI };