const { callSuperWhisperAPI } = require('./superwhisperClient');
const { formatResponse } = require('../utils/responseFormatter');
const logger = require('../utils/logger');
const { Readable } = require('stream');

// Mock dependencies
jest.mock('../utils/logger', () => ({
  info: jest.fn(),
  error: jest.fn(),
  warn: jest.fn(),
  debug: jest.fn(),
}));

jest.mock('../utils/responseFormatter', () => ({
  formatResponse: jest.fn(),
}));

jest.mock('node-fetch');
const fetch = require('node-fetch');

describe('SuperWhisper Client', () => {
  const mockTargetUrl = 'https://api.superwhisper.com/v1/chat';
  const mockRequestBody = { messages: [{ role: 'user', content: 'Hello' }] };
  const mockHeaders = { 'Authorization': 'Bearer test-token' };
  const mockModelName = 'test-model';

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Non-streaming API calls', () => {
    it('should handle successful non-streaming API call', async () => {
      // Create mock readable stream for SSE response
      const mockStream = new Readable({
        read() {
          this.push('data: {"type":"message_start","message":{"id":"msg_123","model":"claude-3"}}\n\n');
          this.push('data: {"type":"content_block_delta","delta":{"type":"text_delta","text":"Hello"}}\n\n');
          this.push('data: {"type":"message_delta","delta":{"stop_reason":"end_turn"}}\n\n');
          this.push('data: {"type":"message_stop"}\n\n');
          this.push(null);
        }
      });

      // Mock fetch response
      fetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
        headers: {
          get: jest.fn().mockReturnValue('text/event-stream'),
          raw: jest.fn().mockReturnValue({})
        },
        body: mockStream
      });

      // Mock formatResponse
      formatResponse.mockReturnValueOnce({ formatted: true });

      const result = await callSuperWhisperAPI(
        mockTargetUrl,
        mockRequestBody,
        mockHeaders,
        mockModelName,
        'anthropic',
        false
      );

      expect(fetch).toHaveBeenCalledWith(mockTargetUrl, {
        method: 'POST',
        headers: mockHeaders,
        body: JSON.stringify(mockRequestBody)
      });

      expect(formatResponse).toHaveBeenCalledWith(
        expect.objectContaining({
          id: 'msg_123',
          model: 'claude-3',
          fullText: 'Hello',
          stopReason: 'end_turn'
        }),
        mockModelName,
        'anthropic'
      );

      expect(result).toEqual({ formatted: true });
      expect(logger.info).toHaveBeenCalledWith(expect.stringContaining('SuperWhisper stream processed'));
    });

    it('should handle API errors', async () => {
      const errorResponse = {
        ok: false,
        status: 400,
        statusText: 'Bad Request',
        text: jest.fn().mockResolvedValue('Invalid request'),
        headers: {
          raw: jest.fn().mockReturnValue({})
        }
      };

      fetch.mockResolvedValueOnce(errorResponse);

      await expect(callSuperWhisperAPI(
        mockTargetUrl,
        mockRequestBody,
        mockHeaders,
        mockModelName,
        'anthropic',
        false
      )).rejects.toThrow('SuperWhisper API request failed');

      expect(logger.error).toHaveBeenCalledWith('SuperWhisper API Error', expect.any(Object));
    });

    it('should handle non-stream content type error', async () => {
      fetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
        headers: {
          get: jest.fn().mockReturnValue('application/json'),
          raw: jest.fn().mockReturnValue({})
        },
        text: jest.fn().mockResolvedValue('{"error": "wrong format"}')
      });

      await expect(callSuperWhisperAPI(
        mockTargetUrl,
        mockRequestBody,
        mockHeaders,
        mockModelName,
        'anthropic',
        false
      )).rejects.toThrow('SuperWhisper API did not return a stream as expected');

      expect(logger.error).toHaveBeenCalledWith(
        'SuperWhisper API did not return a stream. Unexpected Content-Type.',
        expect.any(Object)
      );
    });
  });

  describe('Streaming API calls', () => {
    it('should handle successful streaming API call', async () => {
      const mockResponse = {
        ok: true,
        status: 200,
        headers: {
          get: jest.fn().mockReturnValue('text/event-stream'),
          raw: jest.fn().mockReturnValue({})
        }
      };

      fetch.mockResolvedValueOnce(mockResponse);

      const result = await callSuperWhisperAPI(
        mockTargetUrl,
        mockRequestBody,
        mockHeaders,
        mockModelName,
        'anthropic',
        true
      );

      expect(result).toEqual({ rawSuperWhisperResponse: mockResponse });
      expect(logger.info).toHaveBeenCalledWith(
        'SuperWhisper API raw response received (for client streaming)',
        expect.any(Object)
      );
    });
  });

  describe('Stream accumulation', () => {
    describe('Anthropic format', () => {
      it('should accumulate Anthropic stream events correctly', async () => {
        const mockStream = new Readable({
          read() {
            this.push('data: {"type":"message_start","message":{"id":"msg_123","model":"claude-3","usage":{"input_tokens":10}}}\n\n');
            this.push('data: {"type":"content_block_start","index":0}\n\n');
            this.push('data: {"type":"content_block_delta","delta":{"type":"text_delta","text":"Hello"}}\n\n');
            this.push('data: {"type":"content_block_delta","delta":{"type":"text_delta","text":" World"}}\n\n');
            this.push('data: {"type":"content_block_stop","index":0}\n\n');
            this.push('data: {"type":"message_delta","delta":{"stop_reason":"end_turn"},"usage":{"output_tokens":20}}\n\n');
            this.push('data: {"type":"message_stop"}\n\n');
            this.push(null);
          }
        });

        fetch.mockResolvedValueOnce({
          ok: true,
          status: 200,
          headers: {
            get: jest.fn().mockReturnValue('text/event-stream'),
            raw: jest.fn().mockReturnValue({})
          },
          body: mockStream
        });

        const result = await callSuperWhisperAPI(
          mockTargetUrl,
          mockRequestBody,
          mockHeaders,
          mockModelName,
          'anthropic',
          false
        );

        expect(formatResponse).toHaveBeenCalledWith(
          expect.objectContaining({
            id: 'msg_123',
            model: 'claude-3',
            fullText: 'Hello World',
            stopReason: 'end_turn',
            inputTokens: 10,
            outputTokens: 20
          }),
          mockModelName,
          'anthropic'
        );
      });
    });

    describe('OpenAI format', () => {
      it('should accumulate OpenAI stream chunks correctly', async () => {
        const mockStream = new Readable({
          read() {
            this.push('data: {"id":"chatcmpl-123","model":"gpt-4","choices":[{"delta":{"role":"assistant"}}]}\n\n');
            this.push('data: {"id":"chatcmpl-123","choices":[{"delta":{"content":"Hello"}}]}\n\n');
            this.push('data: {"id":"chatcmpl-123","choices":[{"delta":{"content":" World"}}]}\n\n');
            this.push('data: {"id":"chatcmpl-123","choices":[{"delta":{},"finish_reason":"stop"}]}\n\n');
            this.push('data: [DONE]\n\n');
            this.push(null);
          }
        });

        fetch.mockResolvedValueOnce({
          ok: true,
          status: 200,
          headers: {
            get: jest.fn().mockReturnValue('text/event-stream'),
            raw: jest.fn().mockReturnValue({})
          },
          body: mockStream
        });

        const result = await callSuperWhisperAPI(
          mockTargetUrl,
          mockRequestBody,
          mockHeaders,
          mockModelName,
          'openai',
          false
        );

          expect(formatResponse).toHaveBeenCalledWith(
            {
              chunks: expect.any(Array),
              events: expect.any(Array),
              finishReasonOpenAI: 'stop',
              fullText: 'Hello World',
              id: 'chatcmpl-123',
              inputTokens: 0,
              model: 'test-model',
              outputTokens: 0,
              stopReason: null,
              stopSequence: null
            },
            mockModelName,
            'openai'
          );
      });
    });

    it('should handle stream errors', async () => {
      const mockStream = new Readable({
        read() {
          this.emit('error', new Error('Stream error'));
        }
      });

      fetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
        headers: {
          get: jest.fn().mockReturnValue('text/event-stream'),
          raw: jest.fn().mockReturnValue({})
        },
        body: mockStream
      });

      await expect(callSuperWhisperAPI(
        mockTargetUrl,
        mockRequestBody,
        mockHeaders,
        mockModelName,
        'anthropic',
        false
      )).rejects.toThrow('Stream error');

      expect(logger.error).toHaveBeenCalledWith(
        'Error reading from SuperWhisper stream',
        expect.any(Object)
      );
    });
  });
});
