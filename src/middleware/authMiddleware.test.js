const { authenticateToken } = require('./authMiddleware');
const logger = require('../utils/logger');

// Mock logger to prevent actual logging during tests and allow assertions
jest.mock('../utils/logger', () => ({
  warn: jest.fn(),
  error: jest.fn(),
}));

describe('authenticateToken Middleware', () => {
  let mockRequest;
  let mockResponse;
  let nextFunction;

  beforeEach(() => {
    mockRequest = {
      headers: {},
      ip: '127.0.0.1',
      originalUrl: '/test',
      method: 'POST',
    };
    mockResponse = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn(),
    };
    nextFunction = jest.fn();
    // Clear mock logger calls before each test
    logger.warn.mockClear();
    logger.error.mockClear();
  });

  afterEach(() => {
    delete process.env.PROXY_AUTH_TOKEN; // Clean up env variable
  });

  describe('When PROXY_AUTH_TOKEN is set', () => {
    beforeEach(() => {
      process.env.PROXY_AUTH_TOKEN = 'test-token';
    });

    it('should call next() if token is valid', () => {
      mockRequest.headers['authorization'] = 'Bearer test-token';
      authenticateToken(mockRequest, mockResponse, nextFunction);
      expect(nextFunction).toHaveBeenCalledTimes(1);
      expect(mockResponse.status).not.toHaveBeenCalled();
    });

    it('should return 401 if no token is provided', () => {
      authenticateToken(mockRequest, mockResponse, nextFunction);
      expect(mockResponse.status).toHaveBeenCalledWith(401);
      expect(mockResponse.json).toHaveBeenCalledWith({ error: "Unauthorized", message: "No token provided." });
      expect(nextFunction).not.toHaveBeenCalled();
      expect(logger.warn).toHaveBeenCalledWith('Unauthorized: No token provided', expect.any(Object));
    });

    it('should return 401 if token is invalid', () => {
      mockRequest.headers['authorization'] = 'Bearer invalid-token';
      authenticateToken(mockRequest, mockResponse, nextFunction);
      expect(mockResponse.status).toHaveBeenCalledWith(401);
      expect(mockResponse.json).toHaveBeenCalledWith({ error: "Unauthorized", message: "Invalid token." });
      expect(nextFunction).not.toHaveBeenCalled();
      expect(logger.warn).toHaveBeenCalledWith('Unauthorized: Invalid token', expect.any(Object));
    });

    it('should handle missing Bearer prefix gracefully (considers it no token)', () => {
      mockRequest.headers['authorization'] = 'test-token'; // No "Bearer "
      authenticateToken(mockRequest, mockResponse, nextFunction);
      expect(mockResponse.status).toHaveBeenCalledWith(401);
      expect(mockResponse.json).toHaveBeenCalledWith({ error: "Unauthorized", message: "No token provided." });
      expect(nextFunction).not.toHaveBeenCalled();
    });
  });

  describe('When PROXY_AUTH_TOKEN is NOT set', () => {
    beforeEach(() => {
      // Ensure it's undefined or empty
      delete process.env.PROXY_AUTH_TOKEN;
    });

    it('should return 500 and log an error', () => {
      authenticateToken(mockRequest, mockResponse, nextFunction);
      expect(mockResponse.status).toHaveBeenCalledWith(500);
      expect(mockResponse.json).toHaveBeenCalledWith({
        error: "Server Misconfiguration",
        message: "Authentication is not properly configured on the server."
      });
      expect(nextFunction).not.toHaveBeenCalled();
      expect(logger.error).toHaveBeenCalledWith(
        'CRITICAL: PROXY_AUTH_TOKEN is not set. Request to protected endpoint denied due to server misconfiguration.',
        expect.any(Object)
      );
    });
  });
});