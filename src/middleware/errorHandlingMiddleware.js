const logger = require('../utils/logger');

// This middleware should be registered last in server.js
const globalErrorHandler = (err, req, res, next) => {
  // Log the full error internally, if not already logged in detail
  // The chatCompletionsRouter already logs errors with context, so this can be a fallback
  if (!err.logged) { // Add a flag to error objects if they've been logged in detail elsewhere
    logger.error('Global Error Handler caught an error:', {
      message: err.message,
      stack: err.stack,
      url: req.originalUrl,
      method: req.method,
      // Add any other relevant context
    });
  }


  // Determine status code
  let statusCode = err.status || err.statusCode || 500;
  let clientMessage = "Internal Server Error";

  if (statusCode === 400) {
    clientMessage = "Bad Request";
  } else if (statusCode === 401) {
    clientMessage = "Unauthorized";
  } else if (statusCode === 403) {
    clientMessage = "Forbidden";
  } else if (statusCode === 404) {
    clientMessage = "Not Found";
  }
  // Add more specific status code handling if needed

  // For SuperWhisper API errors specifically, we might have more info
  // but the PRD says "minimal, generic error messages to the client"
  if (err.isSuperWhisperError) {
    // statusCode might already be set from the SW response (e.g. 429, 503)
    // We still send a generic message to our client.
    // The detailed SW error is logged.
  }


  // Send generic error response to client
  res.status(statusCode).json({ error: clientMessage });
};

module.exports = { globalErrorHandler };