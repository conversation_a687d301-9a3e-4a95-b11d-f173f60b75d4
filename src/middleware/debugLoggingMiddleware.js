const logger = require('../utils/logger');

/**
 * This middleware logs the original, untransformed incoming request body
 * if LOG_LEVEL=DEBUG and the 'requestClient' debug option is selected.
 * It should be placed early in the Express middleware stack, right after express.json().
 */
const logOriginalRequest = (req, res, next) => {
  // This function only does something if the server is in debug mode.
  if (process.env.NODE_ENV !== 'test' && process.env.LOG_LEVEL && process.env.LOG_LEVEL.toUpperCase() === 'DEBUG') {
    // We need the model from the body to check against the filter.
    // If there's no body or model, we can't filter, so we skip.
    const modelName = req.body ? req.body.model : undefined;

    if (modelName) {
      logger.debugData('requestClient', {
        timestamp: new Date().toISOString(),
        method: req.method,
        url: req.originalUrl,
        headers: req.headers, // Complete original headers
        body: req.body,       // Complete original body
      }, modelName);
    }
  }
  // Always continue to the next middleware.
  next();
};

module.exports = { logOriginalRequest };