const logger = require('../utils/logger');

// --- Anthropic Client-Side Validation Rules ---
const clientAnthropicAllowedTopLevelKeys = new Set([
  'model',          // string, required
  'messages',       // array, required
  'system',         // string (or array for Cline, but validation is pre-Cline-transform)
  'max_tokens',     // integer
  'metadata',       // object { user_id: string }
  'stop_sequences', // array of strings
  'stream',         // boolean
  'stream_options', // object { include_usage: boolean }
  'temperature',    // number (0.0 to 1.0 typically, but SW might allow wider for some models)
  'top_p',          // number
  'top_k',          // integer
  'tools',          // array of tool objects
  'tool_choice',    // object { type: string, name?: string }
]);

const clientAnthropicRequiredTopLevelKeys = new Set([
  'model',
  'messages',
]);

const clientAnthropicMessageKeys = new Set([
  'role',   // string, required
  'content',// string or array of content parts, required
]);
const clientAnthropicMessageRequiredKeys = new Set([
  'role',
  'content',
]);

// --- OpenAI Client-Side Validation Rules (allowing SuperWhisper specific variations) ---
const clientOpenAIAllowedTopLevelKeys = new Set([
  'model',              // string, required
  'messages',           // array, required
  'stream',             // boolean
  'stream_options',     // object { include_usage: boolean } (SuperWhisper specific for OpenAI)
  'temperature',        // number
  'top_p',              // number
  'max_tokens',         // integer
  'presence_penalty',   // number
  'frequency_penalty',  // number
  'stop',               // string or array of strings
  'tools',              // array of tool objects (OpenAI format)
  'tool_choice',        // string or object (OpenAI format)
  'user',               // string
  'seed',               // integer
  'logprobs',           // boolean
  'top_logprobs',       // integer
]);

const clientOpenAIRequiredTopLevelKeys = new Set([
  'model',
  'messages',
]);

const clientOpenAIMessageKeys = new Set([
  'role',         // string, required
  'content',      // string or array (or null for some assistant messages with tool_calls)
  'name',         // string (for tool role or function name)
  'tool_calls',   // array (for assistant message)
  'tool_call_id', // string (for tool role)
]);
const clientOpenAIMessageRequiredKeys = new Set([
  'role',
  // 'content' is not always required by OpenAI if e.g. tool_calls are present
]);


function validateRequestStructure(req, res, next) {
  const clientRequest = req.body;
  const endpointType = req.endpointType; // Should be set by the router before calling this

  if (!endpointType) {
    logger.error('Validation Error: endpointType not set on request object. This is a server configuration issue.');
    return res.status(500).json({ error: "Server Configuration Error", message: "Cannot determine validation rules." });
  }

  let allowedTopLevelKeys;
  let requiredTopLevelKeys;
  let allowedMessageKeys;
  let requiredMessageKeys;
  let specificLogPrefix;

  if (endpointType === 'anthropic') {
    allowedTopLevelKeys = clientAnthropicAllowedTopLevelKeys;
    requiredTopLevelKeys = clientAnthropicRequiredTopLevelKeys;
    allowedMessageKeys = clientAnthropicMessageKeys;
    requiredMessageKeys = clientAnthropicMessageRequiredKeys;
    specificLogPrefix = 'Anthropic Client Request Validation';
  } else if (endpointType === 'openai') {
    allowedTopLevelKeys = clientOpenAIAllowedTopLevelKeys;
    requiredTopLevelKeys = clientOpenAIRequiredTopLevelKeys;
    allowedMessageKeys = clientOpenAIMessageKeys;
    requiredMessageKeys = clientOpenAIMessageRequiredKeys;
    specificLogPrefix = 'OpenAI Client Request Validation';
  } else {
    logger.warn(`Validation Skipped: Unknown endpointType '${endpointType}' provided. Allowing request to proceed without validation.`);
    return next();
  }

  const errors = [];

  // 1. Check for unknown top-level keys
  for (const key in clientRequest) {
    if (!allowedTopLevelKeys.has(key)) {
      errors.push(`Unknown top-level parameter: '${key}'.`);
    }
  }

  // 2. Check for required top-level keys
  for (const requiredKey of requiredTopLevelKeys) {
    if (!(requiredKey in clientRequest)) {
      errors.push(`Missing required top-level parameter: '${requiredKey}'.`);
    }
  }

  // 3. Validate 'messages' array and its elements if present and valid so far
  if (clientRequest.messages) {
    if (!Array.isArray(clientRequest.messages)) {
      errors.push("'messages' parameter must be an array.");
    } else if (clientRequest.messages.length === 0 && endpointType === 'anthropic') {
      // Anthropic requires at least one message. OpenAI can technically allow zero for some specific cases but generally not for /chat/completions user flows.
      // For simplicity, let's enforce non-empty for Anthropic as per their typical API use.
      errors.push("'messages' array cannot be empty for Anthropic models.");
    } else {
      clientRequest.messages.forEach((message, index) => {
        if (typeof message !== 'object' || message === null) {
          errors.push(`Message at index ${index} must be an object.`);
          return; // Skip further checks for this malformed message
        }
        for (const key in message) {
          if (!allowedMessageKeys.has(key)) {
            errors.push(`Message at index ${index} contains an unknown parameter: '${key}'.`);
          }
        }
        for (const requiredKey of requiredMessageKeys) {
          if (!(requiredKey in message)) {
            errors.push(`Message at index ${index} is missing required parameter: '${requiredKey}'.`);
          }
        }
        // Basic type check for role and content (if content is required and present)
        if (message.role && typeof message.role !== 'string') {
            errors.push(`Message at index ${index} has 'role' with invalid type (must be string).`);
        }
        if (message.content && !(typeof message.content === 'string' || Array.isArray(message.content) || (message.content === null && endpointType === 'openai'))) {
            // OpenAI allows null content if tool_calls are present.
            // Anthropic expects string or array of content parts.
             errors.push(`Message at index ${index} has 'content' with invalid type.`);
        }
      });
    }
  }
  
  // 4. Basic type checks for other known complex types (if they exist)
  if (clientRequest.tools && !Array.isArray(clientRequest.tools)) {
    errors.push("'tools' parameter, if provided, must be an array.");
  }
  if (clientRequest.tool_choice && typeof clientRequest.tool_choice !== 'object' && (endpointType === 'anthropic' || (endpointType === 'openai' && typeof clientRequest.tool_choice !== 'string'))) {
    // OpenAI tool_choice can be string or object. Anthropic is object.
    errors.push(`'tool_choice' parameter, if provided, has an invalid type for ${endpointType}.`);
  }
  if (clientRequest.metadata && typeof clientRequest.metadata !== 'object') {
    errors.push("'metadata' parameter, if provided, must be an object.");
  }
  if (clientRequest.stream_options && typeof clientRequest.stream_options !== 'object') {
    errors.push("'stream_options' parameter, if provided, must be an object.");
  }


  if (errors.length > 0) {
    logger.warn(`${specificLogPrefix} Failed: ${errors.join('; ')}`, {
      clientRequestSummary: { model: clientRequest.model, messageCount: clientRequest.messages ? clientRequest.messages.length : 'N/A' },
      validationErrors: errors,
      ip: req.ip,
      userAgent: req.headers['user-agent']
    });
    return res.status(400).json({
      error: "Bad Request",
      message: "Invalid request structure.",
      details: errors,
    });
  }

  logger.debug(`${specificLogPrefix} Passed.`);
  next();
}

// Exporting the constants primarily for testing or if other parts of the app need them.
module.exports = {
    validateRequestStructure,
    clientAnthropicAllowedTopLevelKeys,
    clientAnthropicRequiredTopLevelKeys,
    clientAnthropicMessageKeys,
    clientAnthropicMessageRequiredKeys,
    clientOpenAIAllowedTopLevelKeys,
    clientOpenAIRequiredTopLevelKeys,
    clientOpenAIMessageKeys,
    clientOpenAIMessageRequiredKeys
};