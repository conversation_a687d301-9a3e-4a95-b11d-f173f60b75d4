const { globalErrorHandler } = require('./errorHandlingMiddleware');
const logger = require('../utils/logger');

// Mock logger
jest.mock('../utils/logger', () => ({
  error: jest.fn(),
}));

describe('Global Error Handler Middleware', () => {
  let mockRequest;
  let mockResponse;
  let nextFunction;

  beforeEach(() => {
    mockRequest = {
      originalUrl: '/test',
      method: 'POST',
    };
    mockResponse = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn(),
    };
    nextFunction = jest.fn();
    // Clear mock logger calls
    logger.error.mockClear();
  });

  describe('Basic error handling', () => {
    it('should handle 400 Bad Request errors', () => {
      const error = new Error('Bad Request');
      error.status = 400;
      
      globalErrorHandler(error, mockRequest, mockResponse, nextFunction);
      
      expect(mockResponse.status).toHaveBeenCalledWith(400);
      expect(mockResponse.json).toHaveBeenCalledWith({ error: 'Bad Request' });
      expect(logger.error).toHaveBeenCalledWith(
        'Global Error Handler caught an error:',
        expect.objectContaining({
          message: error.message,
          stack: error.stack,
          url: mockRequest.originalUrl,
          method: mockRequest.method,
        })
      );
    });

    it('should handle 500 Internal Server Error for unspecified status', () => {
      const error = new Error('Some error');
      
      globalErrorHandler(error, mockRequest, mockResponse, nextFunction);
      
      expect(mockResponse.status).toHaveBeenCalledWith(500);
      expect(mockResponse.json).toHaveBeenCalledWith({ error: 'Internal Server Error' });
      expect(logger.error).toHaveBeenCalledTimes(1);
    });
  });

  describe('Pre-logged errors', () => {
    it('should not log errors that are already logged', () => {
      const error = new Error('Pre-logged error');
      error.status = 400;
      error.logged = true;
      
      globalErrorHandler(error, mockRequest, mockResponse, nextFunction);
      
      expect(mockResponse.status).toHaveBeenCalledWith(400);
      expect(mockResponse.json).toHaveBeenCalledWith({ error: 'Bad Request' });
      expect(logger.error).not.toHaveBeenCalled();
    });
  });

  describe('SuperWhisper errors', () => {
    it('should handle SuperWhisper errors with generic messages', () => {
      const error = new Error('SuperWhisper API rate limit exceeded');
      error.status = 429;
      error.isSuperWhisperError = true;
      
      globalErrorHandler(error, mockRequest, mockResponse, nextFunction);
      
      expect(mockResponse.status).toHaveBeenCalledWith(429);
      expect(mockResponse.json).toHaveBeenCalledWith({ error: 'Internal Server Error' });
      expect(logger.error).toHaveBeenCalledWith(
        'Global Error Handler caught an error:',
        expect.objectContaining({
          message: error.message,
        })
      );
    });

    it('should handle SuperWhisper errors with default 500 status', () => {
      const error = new Error('SuperWhisper API error');
      error.isSuperWhisperError = true;
      
      globalErrorHandler(error, mockRequest, mockResponse, nextFunction);
      
      expect(mockResponse.status).toHaveBeenCalledWith(500);
      expect(mockResponse.json).toHaveBeenCalledWith({ error: 'Internal Server Error' });
      expect(logger.error).toHaveBeenCalled();
    });
  });

  describe('Other status codes', () => {
    it('should handle 401 Unauthorized errors', () => {
      const error = new Error('Unauthorized');
      error.status = 401;
      
      globalErrorHandler(error, mockRequest, mockResponse, nextFunction);
      
      expect(mockResponse.status).toHaveBeenCalledWith(401);
      expect(mockResponse.json).toHaveBeenCalledWith({ error: 'Unauthorized' });
    });

    it('should handle 403 Forbidden errors', () => {
      const error = new Error('Forbidden');
      error.status = 403;
      
      globalErrorHandler(error, mockRequest, mockResponse, nextFunction);
      
      expect(mockResponse.status).toHaveBeenCalledWith(403);
      expect(mockResponse.json).toHaveBeenCalledWith({ error: 'Forbidden' });
    });

    it('should handle 404 Not Found errors', () => {
      const error = new Error('Not Found');
      error.status = 404;
      
      globalErrorHandler(error, mockRequest, mockResponse, nextFunction);
      
      expect(mockResponse.status).toHaveBeenCalledWith(404);
      expect(mockResponse.json).toHaveBeenCalledWith({ error: 'Not Found' });
    });
  });
});
