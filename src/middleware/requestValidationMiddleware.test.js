const { validateRequestStructure } = require('./requestValidationMiddleware');
const logger = require('../utils/logger');

// Mock logger
jest.mock('../utils/logger', () => ({
  warn: jest.fn(),
  debug: jest.fn(),
  error: jest.fn(),
}));

describe('Request Validation Middleware', () => {
  let mockRequest;
  let mockResponse;
  let nextFunction;

  beforeEach(() => {
    mockRequest = {
      body: {},
      ip: '127.0.0.1',
      headers: { 'user-agent': 'test-agent' },
    };
    mockResponse = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn(),
    };
    nextFunction = jest.fn();
    // Clear mock logger calls
    logger.warn.mockClear();
    logger.debug.mockClear();
    logger.error.mockClear();
  });

  describe('Anthropic Request Validation', () => {
    beforeEach(() => {
      mockRequest.endpointType = 'anthropic';
    });

    it('should allow valid Anthropic request', () => {
      mockRequest.body = {
        model: 'claude-3',
        messages: [{
          role: 'user',
          content: 'Hello'
        }]
      };

      validateRequestStructure(mockRequest, mockResponse, nextFunction);

      expect(nextFunction).toHaveBeenCalledTimes(1);
      expect(mockResponse.status).not.toHaveBeenCalled();
      expect(logger.debug).toHaveBeenCalledWith('Anthropic Client Request Validation Passed.');
    });

    it('should reject request missing required fields', () => {
      mockRequest.body = {
        messages: [{
          role: 'user',
          content: 'Hello'
        }]
      };

      validateRequestStructure(mockRequest, mockResponse, nextFunction);

      expect(mockResponse.status).toHaveBeenCalledWith(400);
      expect(mockResponse.json).toHaveBeenCalledWith(
        expect.objectContaining({
          error: 'Bad Request',
          details: expect.arrayContaining([
            expect.stringContaining("Missing required top-level parameter: 'model'")
          ])
        })
      );
      expect(nextFunction).not.toHaveBeenCalled();
    });

    it('should reject request with invalid message structure', () => {
      mockRequest.body = {
        model: 'claude-3',
        messages: [{
          role: 'user'
          // missing content
        }]
      };

      validateRequestStructure(mockRequest, mockResponse, nextFunction);

      expect(mockResponse.status).toHaveBeenCalledWith(400);
      expect(mockResponse.json).toHaveBeenCalledWith(
        expect.objectContaining({
          error: 'Bad Request',
          details: expect.arrayContaining([
            expect.stringContaining("Message at index 0 is missing required parameter: 'content'")
          ])
        })
      );
    });

    it('should reject empty messages array for Anthropic', () => {
      mockRequest.body = {
        model: 'claude-3',
        messages: []
      };

      validateRequestStructure(mockRequest, mockResponse, nextFunction);

      expect(mockResponse.status).toHaveBeenCalledWith(400);
      expect(mockResponse.json).toHaveBeenCalledWith(
        expect.objectContaining({
          details: expect.arrayContaining([
            "'messages' array cannot be empty for Anthropic models."
          ])
        })
      );
    });
  });

  describe('OpenAI Request Validation', () => {
    beforeEach(() => {
      mockRequest.endpointType = 'openai';
    });

    it('should allow valid OpenAI request', () => {
      mockRequest.body = {
        model: 'gpt-4',
        messages: [{
          role: 'user',
          content: 'Hello'
        }]
      };

      validateRequestStructure(mockRequest, mockResponse, nextFunction);

      expect(nextFunction).toHaveBeenCalledTimes(1);
      expect(mockResponse.status).not.toHaveBeenCalled();
      expect(logger.debug).toHaveBeenCalledWith('OpenAI Client Request Validation Passed.');
    });

    it('should allow OpenAI message with null content and tool_calls', () => {
      mockRequest.body = {
        model: 'gpt-4',
        messages: [{
          role: 'assistant',
          content: null,
          tool_calls: [{
            id: 'call_123',
            type: 'function',
            function: {
              name: 'test_function',
              arguments: '{}'
            }
          }]
        }]
      };

      validateRequestStructure(mockRequest, mockResponse, nextFunction);

      expect(nextFunction).toHaveBeenCalledTimes(1);
      expect(mockResponse.status).not.toHaveBeenCalled();
    });

    it('should reject request missing required fields', () => {
      mockRequest.body = {
        messages: [{
          role: 'user',
          content: 'Hello'
        }]
      };

      validateRequestStructure(mockRequest, mockResponse, nextFunction);

      expect(mockResponse.status).toHaveBeenCalledWith(400);
      expect(mockResponse.json).toHaveBeenCalledWith(
        expect.objectContaining({
          details: expect.arrayContaining([
            expect.stringContaining("Missing required top-level parameter: 'model'")
          ])
        })
      );
    });
  });

  describe('Common Validation Rules', () => {
    beforeEach(() => {
      mockRequest.endpointType = 'anthropic'; // Use Anthropic as default for common tests
    });

    it('should reject invalid message type', () => {
      mockRequest.body = {
        model: 'claude-3',
        messages: 'not an array'
      };

      validateRequestStructure(mockRequest, mockResponse, nextFunction);

      expect(mockResponse.status).toHaveBeenCalledWith(400);
      expect(mockResponse.json).toHaveBeenCalledWith(
        expect.objectContaining({
          details: expect.arrayContaining([
            "'messages' parameter must be an array."
          ])
        })
      );
    });

    it('should reject invalid tools type', () => {
      mockRequest.body = {
        model: 'claude-3',
        messages: [{ role: 'user', content: 'Hello' }],
        tools: 'not an array'
      };

      validateRequestStructure(mockRequest, mockResponse, nextFunction);

      expect(mockResponse.status).toHaveBeenCalledWith(400);
      expect(mockResponse.json).toHaveBeenCalledWith(
        expect.objectContaining({
          details: expect.arrayContaining([
            "'tools' parameter, if provided, must be an array."
          ])
        })
      );
    });

    it('should reject invalid stream_options type', () => {
      mockRequest.body = {
        model: 'claude-3',
        messages: [{ role: 'user', content: 'Hello' }],
        stream_options: 'not an object'
      };

      validateRequestStructure(mockRequest, mockResponse, nextFunction);

      expect(mockResponse.status).toHaveBeenCalledWith(400);
      expect(mockResponse.json).toHaveBeenCalledWith(
        expect.objectContaining({
          details: expect.arrayContaining([
            "'stream_options' parameter, if provided, must be an object."
          ])
        })
      );
    });
  });

  describe('Error Handling', () => {
    it('should handle missing endpointType', () => {
      mockRequest.endpointType = undefined;
      mockRequest.body = {
        model: 'claude-3',
        messages: [{ role: 'user', content: 'Hello' }]
      };

      validateRequestStructure(mockRequest, mockResponse, nextFunction);

      expect(mockResponse.status).toHaveBeenCalledWith(500);
      expect(mockResponse.json).toHaveBeenCalledWith({
        error: 'Server Configuration Error',
        message: 'Cannot determine validation rules.'
      });
      expect(logger.error).toHaveBeenCalledWith(
        'Validation Error: endpointType not set on request object. This is a server configuration issue.'
      );
    });

    it('should skip validation for unknown endpointType', () => {
      mockRequest.endpointType = 'unknown';
      mockRequest.body = {
        model: 'unknown-model',
        messages: [{ role: 'user', content: 'Hello' }]
      };

      validateRequestStructure(mockRequest, mockResponse, nextFunction);

      expect(nextFunction).toHaveBeenCalledTimes(1);
      expect(logger.warn).toHaveBeenCalledWith(
        "Validation Skipped: Unknown endpointType 'unknown' provided. Allowing request to proceed without validation."
      );
    });
  });
});
