// src/middleware/claudeCodeMiddleware.js

const logger = require('../utils/logger');

const CLAUDE_CLI_USER_AGENT_SUBSTRING = 'claude-cli';
const ZED_USER_AGENT_SUBSTRING = 'zed';
const CLAUDE_CODE_HAIKU_MODEL = 'claude-3-5-haiku-20241022';
const CLAUDE_CODE_HAIKU_RATE_LIMIT_MS = 1000;

let lastClaudeCodeHaikuRequestTimestamp = 0;

const ensureAllowedClient = (req, res, next) => {
  const userAgent = req.headers['user-agent'] || '';
  const userAgentLower = userAgent.toLowerCase();
  
  const isClaudeCode = userAgentLower.includes(CLAUDE_CLI_USER_AGENT_SUBSTRING);
  const isZed = userAgentLower.includes(ZED_USER_AGENT_SUBSTRING);
  
  if (isClaudeCode || isZed) {
    const clientType = isClaudeCode ? 'Claude Code' : 'Zed';
    logger.info(`${clientType} client detected: ${userAgent}`);
    next();
  } else {
    logger.warn(`Access to /v1/messages denied for unauthorized User-Agent: ${userAgent}`, {
        ip: req.ip,
        url: req.originalUrl,
    });
    res.status(403).json({ error: 'Forbidden', message: 'Access restricted to authorized clients (Claude Code, Zed).' });
  }
};

const claudeCodeRateLimiter = (req, res, next) => {
  const requestModel = req.body && req.body.model;

  if (requestModel === CLAUDE_CODE_HAIKU_MODEL) {
    const now = Date.now();
    if (now - lastClaudeCodeHaikuRequestTimestamp < CLAUDE_CODE_HAIKU_RATE_LIMIT_MS) {
      logger.warn(`Rate limit exceeded for Claude Code Haiku model. Last request: ${lastClaudeCodeHaikuRequestTimestamp}, Current: ${now}`, {
        ip: req.ip,
        model: requestModel,
        userAgent: req.headers['user-agent'],
      });
      return res.status(503).json({
        type: "error",
        error: {
          type: "overloaded_error",
          message: "The server is temporarily overloaded. Please try again shortly."
        }
      });
    }
    lastClaudeCodeHaikuRequestTimestamp = now;
    logger.debug(`Claude Code Haiku request allowed. Timestamp updated to: ${now}`);
  }
  next();
};

/**
 * Transforms the request body from Claude CLI to a format the proxy can handle.
 * THIS IS THE CORRECTED AND SIMPLIFIED VERSION.
 * 1. Moves the `tools` array into the `system` prompt using your required XML format.
 * 2. Processes the `messages` array with a single, consistent rule:
 *    - If a message's `content` is an array, it is ALWAYS stringified. This avoids
 *      the fragile logic of splitting some messages while stringifying others,
 *      which was causing inconsistent request payloads in multi-turn conversations.
 */
const transformClaudeCliRequest = (req, res, next) => {
  try {
    const { body } = req;
    logger.info('Applying UNIFIED Claude CLI request transformations.');

    // 1. Inject tools into the system prompt (logic remains the same)
    if (body.tools && Array.isArray(body.tools) && body.system && Array.isArray(body.system) && body.system.length > 1) {
      const stringifiedTools = JSON.stringify(body.tools);
      const systemPromptObject = body.system[1];

      if (systemPromptObject && systemPromptObject.text) {
        systemPromptObject.text = systemPromptObject.text.replace(
          'tools available to you to assist the user',
          `<tools>${stringifiedTools}</tools> available to you to assist the user. VERY IMPORTANT: When responding with a tool_use, you MUST use XML tags in this format: "<tool_use>\n<name>{tool-name}</name>\n<parameter name=\"file_path\">/{file-path}</parameter>\n<parameter name=\"content\">{content}></parameter>\n</tool_use>"`
        );
        logger.debug('Successfully injected tools into system prompt.');
      }
      delete body.tools;
      logger.debug('Removed top-level tools array.');
    }

    // 2. Transform the messages array with the new, simpler logic
    if (body.messages && Array.isArray(body.messages)) {
      // Create a new array by mapping over the original messages.
      body.messages = body.messages.map(message => {
        // The ONLY rule: if content is an array, stringify it.
        if (Array.isArray(message.content)) {
          logger.debug('Stringifying message content array.', { role: message.role });
          return {
            role: message.role,
            content: JSON.stringify(message.content),
          };
        }
        // Otherwise, keep the message as is.
        return message;
      });
      logger.info('Finished transforming messages array with unified logic.');
    }

    req.body = body;
    next();
  } catch (error) {
    logger.error('Error during Claude CLI request transformation:', {
      error: error.message,
      stack: error.stack,
    });
    next(error);
  }
};


module.exports = {
  ensureAllowedClient,
  ensureClaudeCliAgent: ensureAllowedClient, // Backward compatibility alias
  claudeCodeRateLimiter,
  transformClaudeCliRequest,
};