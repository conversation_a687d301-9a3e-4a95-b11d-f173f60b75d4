const logger = require('../utils/logger');

const authenticateToken = (req, res, next) => {
  const authHeader = req.headers['authorization'];
  const xApiKey = req.headers['x-api-key'];
  
  // Extract token from Authorization header (Bearer TOKEN) or x-api-key header
  let token = null;
  let authMethod = null;
  
  if (authHeader && authHeader.startsWith('Bearer ')) {
    token = authHeader.split(' ')[1];
    authMethod = 'Bearer';
  } else if (xApiKey) {
    token = xApiKey;
    authMethod = 'x-api-key';
  }

  const proxyAuthToken = process.env.PROXY_AUTH_TOKEN;

  if (!proxyAuthToken) {
    // If PROXY_AUTH_TOKEN is not set, the server is misconfigured for secure operations.
    // Log this as an error. The startup warning in cli.js also highlights this.
    logger.error('CRITICAL: PROXY_AUTH_TOKEN is not set. Request to protected endpoint denied due to server misconfiguration.', {
      ip: req.ip,
      url: req.originalUrl,
      method: req.method
    });
    return res.status(500).json({ error: "Server Misconfiguration", message: "Authentication is not properly configured on the server." });
  }

  if (token == null) {
    logger.warn('Unauthorized: No token provided', {
      ip: req.ip,
      url: req.originalUrl,
      method: req.method,
      hasAuthHeader: !!authHeader,
      hasXApiKey: !!xApiKey
    });
    return res.status(401).json({ error: "Unauthorized", message: "No token provided. Use either 'Authorization: Bearer <token>' or 'x-api-key: <token>' header." });
  }

  if (token === proxyAuthToken) {
    logger.debug(`Authentication successful using ${authMethod} method`, {
      ip: req.ip,
      url: req.originalUrl,
      authMethod
    });
    next(); // Token is valid
  } else {
    logger.warn('Unauthorized: Invalid token', {
        ip: req.ip,
        url: req.originalUrl,
        method: req.method,
        authMethod
    });
    return res.status(401).json({ error: "Unauthorized", message: "Invalid token." });
  }
};

module.exports = { authenticateToken };