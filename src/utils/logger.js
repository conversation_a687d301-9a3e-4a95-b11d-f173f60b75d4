const fs = require('fs');
const path = require('path');
const { PassThrough } = require('stream');
const { modelsData } = require('../config/models'); 

// --- START: NEW DEBUG LOGIC ---

// Configuration for the new debug logger, populated on startup if LOG_LEVEL=DEBUG
const debugLogConfig = {
    // Will be populated with booleans, e.g., requestClient: true
};
const debugOptions = ['requestClient', 'requestProxy', 'responseSW', 'responseProxy'];
const logsDir = path.join(process.cwd(), 'logs');
const debugLogsDir = path.join(logsDir, 'debug');

// Interactive setup runs only if LOG_LEVEL=DEBUG and not in a test environment
if (process.env.NODE_ENV !== 'test' && process.env.LOG_LEVEL && process.env.LOG_LEVEL.toUpperCase() === 'DEBUG') {
  try {
    const readlineSync = require('readline-sync');
    // We need the model list for the prompt
    const { modelsData } = require('../config/models');
    const modelNames = modelsData.data.map(m => m.id);

    console.log('\n--- INTERACTIVE DEBUG LOGGING SETUP ---');

    // --- Step 1: Select Log Points ---
    console.log('Select which points to log to separate files in logs/debug/:');
    const questionPoints = debugOptions.map((opt, i) => `  ${i + 1}. ${opt}`).join('\n');
    console.log(questionPoints);
    const answerPoints = readlineSync.question('Enter numbers separated by commas (e.g., "1, 3", or "all"): ');

    const pointSelections = new Set();
    if (answerPoints.toLowerCase() === 'all') {
      debugOptions.forEach((opt, i) => pointSelections.add(i + 1));
    } else {
      answerPoints.split(',').forEach(numStr => {
        const num = parseInt(numStr.trim(), 10);
        if (!isNaN(num) && num >= 1 && num <= debugOptions.length) {
          pointSelections.add(num);
        }
      });
    }
    
    debugOptions.forEach((opt, i) => {
      debugLogConfig[opt] = pointSelections.has(i + 1);
    });

    const enabledPoints = Object.keys(debugLogConfig).filter(k => debugLogConfig[k]);

    // --- Step 2: Select Models (only if at least one log point was selected) ---
    if (enabledPoints.length > 0) {
      console.log('\nSelect which models to filter for (only requests for these models will be logged):');
      const questionModels = modelNames.map((name, i) => `  ${i + 1}. ${name}`).join('\n');
      console.log(questionModels);
      const answerModels = readlineSync.question('Enter numbers for the models to log, separated by commas (e.g., "1, 3", or "all"): ');

      const modelSelections = new Set();
      if (answerModels.toLowerCase() === 'all') {
          modelNames.forEach(name => modelSelections.add(name));
      } else {
          answerModels.split(',').forEach(numStr => {
              const num = parseInt(numStr.trim(), 10);
              if (!isNaN(num) && num >= 1 && num <= modelNames.length) {
                  modelSelections.add(modelNames[num - 1]);
              }
          });
      }
      debugLogConfig.models = modelSelections; // Store the Set of selected model names

      if (modelSelections.size > 0) {
          if (!fs.existsSync(debugLogsDir)) {
            fs.mkdirSync(debugLogsDir, { recursive: true });
          }
          console.log(`\nDebug logging enabled for models: ${[...modelSelections].join(', ')}.`);
      } else {
          console.log('\nNo models selected. All file-based debug logging will be skipped.');
      }
    }

    console.log(`Debug logging to file enabled for points: ${enabledPoints.length > 0 ? enabledPoints.join(', ') : 'None'}.`);
    console.log('--- SERVER STARTING ---');

  } catch (e) {
    console.warn('\nCould not initialize interactive debug setup. `readline-sync` may be missing or this is not an interactive shell.');
    console.warn('File-based debug logging will be disabled. Error:', e.message);
    console.log('--- SERVER STARTING ---');
  }
}

/**
 * Checks if debug logging is active for a given type and model.
 * @param {string} logType - One of the debugOptions.
 * @param {string} modelName - The name of the model for the current request.
 * @returns {boolean}
 */
function isDebugLoggingActive(logType, modelName) {
    if (!modelName || !debugLogConfig.models || debugLogConfig.models.size === 0) {
        return false;
    }
    return debugLogConfig[logType] === true && debugLogConfig.models.has(modelName);
}

/**
 * Writes serializable data to a debug log file if the type and model are enabled.
 * @param {string} logType - One of the debugOptions.
 * @param {object} data - The JSON-serializable object to log.
 * @param {string} modelName - The model name to filter by.
 */
function debugData(logType, data, modelName) {
  if (!isDebugLoggingActive(logType, modelName)) return;

  try {
    const timestamp = new Date().toISOString().replace(/:/g, '-'); // Sanitize for filename
    const filePath = path.join(debugLogsDir, `${logType}-${timestamp}.log`);
    const content = JSON.stringify(data, null, 2);
    fs.writeFile(filePath, content, (err) => {
        if (err) console.error(`Failed to write debug log for ${logType}:`, err);
    });
  } catch (e) {
    console.error(`Error creating debug log for ${logType}:`, e.message);
  }
}

/**
 * Creates a PassThrough stream that tees (duplicates) data to a debug log file.
 * @param {string} logType - One of the debugOptions.
 * @param {object} metadata - A JSON-serializable object to write at the top of the file.
 * @param {string} modelName - The model name to filter by.
 * @returns {stream.PassThrough} A stream to use in a pipe chain.
 */
function debugTee(logType, metadata = {}, modelName) {
  // If this log type or model is not enabled, return a simple PassThrough that does nothing.
  if (!isDebugLoggingActive(logType, modelName)) {
    return new PassThrough();
  }

  const timestamp = new Date().toISOString().replace(/:/g, '-');
  const filePath = path.join(debugLogsDir, `${logType}-${timestamp}.log`);
  const fileStream = fs.createWriteStream(filePath);

  const header = JSON.stringify({ ...metadata, debugLogTimestamp: new Date().toISOString() }, null, 2);
  fileStream.write(header + '\n\n--- STREAM BODY START ---\n\n');

  const tee = new PassThrough();
  tee.pipe(fileStream); // All data written to 'tee' will also be written to the file.
  
  tee.on('end', () => {
    if (!fileStream.writableEnded) {
        fileStream.end('\n--- STREAM BODY END ---\n');
    }
  });
  
  return tee;
}

/**
 * Instruments an Express response object to log its output stream to a debug file.
 * @param {object} res - The Express response object.
 * @param {string} logType - The type of log (e.g., 'responseProxy').
 * @param {string} modelName - The model name to filter by.
 * @returns {object} The same Express response object, now instrumented.
 */
function instrumentResponseForDebug(res, logType, modelName) {
    if (!isDebugLoggingActive(logType, modelName)) {
        return res;
    }
    
    // Create the tee stream which will write to the log file.
    const teeStream = debugTee(logType, {
        note: `This log contains the raw body of the HTTP response stream sent to the client. Headers are set on the response object before this stream begins.`,
        initialHeaders: res.getHeaders(),
    }, modelName); // Pass modelName to debugTee

    const originalWrite = res.write;
    const originalEnd = res.end;

    // Override res.write to also write to our tee
    res.write = function(chunk, encoding, callback) {
        if (chunk) {
            teeStream.write(chunk, encoding);
        }
        return originalWrite.apply(res, arguments);
    };
    
    // Override res.end to also signal the end of the tee stream
    res.end = function(chunk, encoding, callback) {
        if (chunk) {
            teeStream.end(chunk, encoding);
        } else {
            teeStream.end();
        }
        return originalEnd.apply(res, arguments);
    };
    
    return res;
}


// --- END: NEW DEBUG LOGIC ---


// Create logs directory if it doesn't exist
if (!fs.existsSync(logsDir)) {
  fs.mkdirSync(logsDir, { recursive: true });
}

// Function to get current log file path based on date
function getLogFilePath() {
  const today = new Date();
  const dateString = today.toISOString().split('T')[0]; // YYYY-MM-DD format
  return path.join(logsDir, `${dateString}.log`);
}

// Simple console logging for different levels
const logLevels = {
  DEBUG: 0,
  INFO: 1,
  WARN: 2,
  ERROR: 3,
};

// Get current log level (dynamically from ENV)
function getCurrentLogLevel() {
  const envLevel = process.env.LOG_LEVEL || 'INFO';
  return logLevels[envLevel.toUpperCase()] !== undefined
    ? logLevels[envLevel.toUpperCase()]
    : logLevels.INFO;
}


function formatLogMessage(level, message, details = {}) {
  const timestamp = new Date().toISOString();
  let logEntry = `${timestamp} [${level}] ${message}`;

  if (Object.keys(details).length > 0) {
    // Sanitize details for logging to avoid circular references or overly large objects
    try {
      const sanitizedDetails = JSON.stringify(details, (key, value) => {
        const lowerKey = key.toLowerCase();
        if (typeof value === 'string') {
          if (lowerKey === 'authorization') return 'Bearer [REDACTED]';
          if (lowerKey === 'token' || lowerKey.endsWith('_token') || lowerKey.endsWith('-token')) return '[REDACTED_TOKEN]';
          if (lowerKey === 'key' || lowerKey === 'apikey' || lowerKey === 'api_key' || lowerKey === 'api-key' || lowerKey === 'x-api-key') return '[REDACTED_KEY]';
          if (lowerKey === 'license' || lowerKey.endsWith('_license') || lowerKey.endsWith('-license')) return '[REDACTED_LICENSE]';
          if (lowerKey === 'signature' || lowerKey.endsWith('_signature') || lowerKey.endsWith('-signature')) return '[REDACTED_SIGNATURE]';
          if (lowerKey === 'x-id') return '[REDACTED_X-ID]';
        }
        if (typeof value === 'string' && value.length > 1000) {
          return value.substring(0, 1000) + '... [TRUNCATED]';
        }
        return value;
      }, 2);
      logEntry += `\nDetails: ${sanitizedDetails}`;
    } catch (e) {
      logEntry += `\nDetails: (Error serializing details: ${e.message})`;
    }
  }
  return logEntry + '\n';
}

function writeToLogFile(logEntry) {
  const currentLogFilePath = getLogFilePath();
  fs.appendFile(currentLogFilePath, logEntry, (err) => {
    if (err) {
      console.error('Failed to write to log file:', err);
      console.log(logEntry.trim());
    }
  });
}

const logger = {
  debug: (message, details) => {
    if (getCurrentLogLevel() <= logLevels.DEBUG) {
      const entry = formatLogMessage('DEBUG', message, details);
      console.debug(entry.trim());
      writeToLogFile(entry);
    }
  },
  info: (message, details) => {
    if (getCurrentLogLevel() <= logLevels.INFO) {
      const entry = formatLogMessage('INFO', message, details);
      console.log(entry.trim());
      writeToLogFile(entry);
    }
  },
  warn: (message, details) => {
    if (getCurrentLogLevel() <= logLevels.WARN) {
      const entry = formatLogMessage('WARN', message, details);
      console.warn(entry.trim());
      writeToLogFile(entry);
    }
  },
  error: (message, details) => {
    if (getCurrentLogLevel() <= logLevels.ERROR) {
      let errorDetails = details;
      if (details instanceof Error) {
        errorDetails = { message: details.message, stack: details.stack, ...details };
      }
      const entry = formatLogMessage('ERROR', message, errorDetails);
      console.error(entry.trim());
      writeToLogFile(entry);
    }
  },
  logRequestResponseCycle: (cycleDetails) => {
    const entry = formatLogMessage('CYCLE', 'Request-Response Cycle Summary', cycleDetails);
    writeToLogFile(entry);
  },
  // NEW functions for detailed debug logging
  debugData,
  debugTee,
  instrumentResponseForDebug,
};

// Initial log message
if (process.env.NODE_ENV !== 'test') {
    logger.info(`Logger initialized. Log level: ${process.env.LOG_LEVEL || 'INFO'}. Logs will be written to: ${getLogFilePath()}`);
}


module.exports = logger;