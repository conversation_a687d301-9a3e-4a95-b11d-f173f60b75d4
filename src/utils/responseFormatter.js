const logger = require('./logger');

/**
 * Formats the accumulated stream data into the final non-streaming JSON response
 * emulating either OpenAI or Anthropic native API response.
 *
 * @param {object} accumulatedData - The data accumulated from the SuperWhisper stream.
 *  Contains: id, model, fullText, stopReason, stopSequence, inputTokens, outputTokens, chunks, finishReasonOpenAI
 * @param {string} clientModelName - The original model name requested by the client.
 * @param {string} endpointType - 'openai' or 'anthropic', determines response structure.
 * @returns {object} The formatted JSON response.
 */
function formatResponse(accumulatedData, clientModelName, endpointType) {
  const timestamp = Math.floor(Date.now() / 1000); // Unix epoch seconds

  if (endpointType === 'anthropic') {
    // Use model from stream if available, otherwise fallback to client-requested (mapped) model
    // PRD 4.1.6 says "Actual model name from SuperWhisper stream (e.g., message_start.message.model)"
    // And also "model": "claude-3-5-sonnet-20241022" // Actual model name from SuperWhisper stream
    // This implies we should use the clientModelName here, as the SW stream might return `sw-claude-...`
    // Let's stick to clientModelName for the final response model field as per example.
    // accumulatedData.model might be sw-claude-..., clientModelName is claude-...
    // If SuperWhisper could return a *different base model* than requested, then accumulatedData.model would be more accurate.
    // For now, PRD examples show the original client-facing model name in the response.

    const response = {
      id: accumulatedData.id || `msg_proxy_${Date.now()}`, // Fallback ID
      type: "message",
      role: "assistant",
      model: clientModelName, // As per PRD example, use the client-requested model name
      content: [
        {
          type: "text",
          text: accumulatedData.fullText,
        },
      ],
      stop_reason: accumulatedData.stopReason || "end_turn", // Default if not set
      stop_sequence: accumulatedData.stopSequence, // Will be null if not present
      usage: {
        input_tokens: accumulatedData.inputTokens || 0,
        output_tokens: accumulatedData.outputTokens || 0, // This should be set by message_delta
      },
    };
    logger.debug('Formatted Anthropic-style response', { response });
    return response;

  } else if (endpointType === 'openai') {
    // For OpenAI, the ID and model usually come from the first/any chunk.
    // If accumulatedData.id is null, we need a fallback.
    // accumulatedData.model may have been updated by SW stream. If not, use clientModelName.
    // PRD: "model": "gpt-4.1-2025-04-14", // Actual model name from SuperWhisper stream chunk's model
    // This means we should use accumulatedData.model if it was updated from the stream.
    let responseModel = accumulatedData.model;
    if (responseModel === 'sw-gpt-4.1' && clientModelName === 'gpt-4.1-2025-04-14') {
      responseModel = clientModelName; // Prefer client-facing name if it's just the SW prefix difference
    } else if (responseModel.startsWith('sw-') && clientModelName.includes(responseModel.substring(3))) {
        responseModel = clientModelName; // General case for SW prefix
    }


    const response = {
      id: accumulatedData.id || `chatcmpl-proxy_${Date.now()}`, // Fallback ID
      object: "chat.completion",
      created: timestamp,
      model: responseModel, // Use model from stream if available and different, else client requested
      choices: [
        {
          index: 0,
          message: {
            role: "assistant",
            content: accumulatedData.fullText,
          },
          finish_reason: accumulatedData.finishReasonOpenAI || "stop", // Default if not set
        },
      ],
      // PRD: "usage": { // This field will be omitted, or all token counts set to 0 or null. }
      // Let's omit it entirely as it's simpler and explicitly allowed.
      // If SuperWhisper ever provides token counts in its OpenAI-style stream, this could be revisited.
      // usage: {
      //   prompt_tokens: 0,
      //   completion_tokens: 0,
      //   total_tokens: 0,
      // },
    };
    logger.debug('Formatted OpenAI-style response', { response });
    return response;
  } else {
    logger.error('Unknown endpointType for response formatting', { endpointType });
    throw new Error('Cannot format response: Unknown endpoint type.');
  }
}

module.exports = { formatResponse };