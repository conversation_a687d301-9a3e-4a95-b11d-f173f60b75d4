const { formatResponse } = require('./responseFormatter');
const logger = require('./logger');

jest.mock('./logger', () => ({
  debug: jest.fn(),
  error: jest.fn(),
  info: jest.fn(), // Added info to mock, as responseFormatter might call logger.info
}));

// Mock Date.now() for consistent timestamps
const mockDateNow = jest.spyOn(Date, 'now');

describe('formatResponse', () => {
  const clientModelNameAnthropic = 'claude-3-5-sonnet-20241022';
  const clientModelNameOpenAI = 'gpt-4.1-2025-04-14';
  const mockTimestampMillis = 1678886400000; // March 15, 2023 12:00:00 PM UTC
  const mockTimestampSecs = Math.floor(mockTimestampMillis / 1000);

  beforeEach(() => {
    logger.debug.mockClear();
    logger.error.mockClear();
    logger.info.mockClear();
    mockDateNow.mockReturnValue(mockTimestampMillis);
  });

  afterAll(() => {
    mockDateNow.mockRestore();
  });

  describe('Anthropic EndpointType', () => {
    const accumulatedData = {
      id: 'msg_123',
      model: 'sw-claude-3-5-sonnet', // Actual model from SW
      fullText: 'This is the response text.',
      stopReason: 'end_turn',
      stopSequence: null,
      inputTokens: 10,
      outputTokens: 20,
      chunks: [],
      finishReasonOpenAI: null,
    };

    it('should format response correctly for Anthropic', () => {
      const response = formatResponse(accumulatedData, clientModelNameAnthropic, 'anthropic');
      expect(response).toEqual({
        id: 'msg_123',
        type: 'message',
        role: 'assistant',
        model: clientModelNameAnthropic, // Should use client-requested model name
        content: [{ type: 'text', text: 'This is the response text.' }],
        stop_reason: 'end_turn',
        stop_sequence: null,
        usage: { input_tokens: 10, output_tokens: 20 },
      });
      expect(logger.debug).toHaveBeenCalledWith('Formatted Anthropic-style response', { response: expect.any(Object) });
    });

    it('should use fallback ID if not present in accumulatedData for Anthropic', () => {
        const dataWithoutId = { ...accumulatedData, id: null };
        const response = formatResponse(dataWithoutId, clientModelNameAnthropic, 'anthropic');
        expect(response.id).toBe(`msg_proxy_${mockTimestampMillis}`);
    });

    it('should use default stop_reason if not present for Anthropic', () => {
        const dataWithoutStopReason = { ...accumulatedData, stopReason: null };
        const response = formatResponse(dataWithoutStopReason, clientModelNameAnthropic, 'anthropic');
        expect(response.stop_reason).toBe('end_turn');
    });

     it('should default tokens to 0 if not present in accumulatedData for Anthropic', () => {
        const dataWithoutTokens = { 
            ...accumulatedData, 
            inputTokens: undefined, 
            outputTokens: undefined 
        };
        const response = formatResponse(dataWithoutTokens, clientModelNameAnthropic, 'anthropic');
        expect(response.usage.input_tokens).toBe(0);
        expect(response.usage.output_tokens).toBe(0);
    });
  });

  describe('OpenAI EndpointType', () => {
    const accumulatedData = {
      id: 'chatcmpl-abc',
      model: 'sw-gpt-4.1', // Actual model from SW stream
      fullText: 'This is the OpenAI response.',
      finishReasonOpenAI: 'stop',
      stopReason: null,
      stopSequence: null,
      inputTokens: 0,
      outputTokens: 0,
      chunks: [{ id: 'chatcmpl-abc', model: 'sw-gpt-4.1' }],
    };

    it('should format response correctly for OpenAI', () => {
      const response = formatResponse(accumulatedData, clientModelNameOpenAI, 'openai');
      expect(response).toEqual({
        id: 'chatcmpl-abc',
        object: 'chat.completion',
        created: mockTimestampSecs,
        model: clientModelNameOpenAI, // Should use client-facing name if SW model is just prefixed
        choices: [
          {
            index: 0,
            message: { role: 'assistant', content: 'This is the OpenAI response.' },
            finish_reason: 'stop',
          },
        ],
        // No 'usage' field as per PRD
      });
      expect(logger.debug).toHaveBeenCalledWith('Formatted OpenAI-style response', { response: expect.any(Object) });
    });

    it('should use fallback ID if not present in accumulatedData for OpenAI', () => {
        const dataWithoutId = { ...accumulatedData, id: null };
        const response = formatResponse(dataWithoutId, clientModelNameOpenAI, 'openai');
        expect(response.id).toBe(`chatcmpl-proxy_${mockTimestampMillis}`);
    });
    
    it('should use default finish_reason if not present for OpenAI', () => {
        const dataWithoutFinishReason = { ...accumulatedData, finishReasonOpenAI: null };
        const response = formatResponse(dataWithoutFinishReason, clientModelNameOpenAI, 'openai');
        expect(response.choices[0].finish_reason).toBe('stop');
    });

    it('should use actual SW model name if it is substantially different and not just sw- prefix', () => {
      const dataWithDifferentModel = { ...accumulatedData, model: 'gpt-4-turbo-preview' };
      const response = formatResponse(dataWithDifferentModel, clientModelNameOpenAI, 'openai');
      expect(response.model).toBe('gpt-4-turbo-preview');
    });

    it('should map sw-prefixed model to clientModelName if it corresponds', () => {
      const dataWithSwModel = { ...accumulatedData, model: 'sw-something-else' };
      const clientModel = 'something-else-client'; // Assume mapping exists that would lead to this
      // Test the logic: "if (responseModel.startsWith('sw-') && clientModelName.includes(responseModel.substring(3)))"
      
      // Case 1: Direct match after removing sw-
      let response = formatResponse({ ...accumulatedData, model: 'sw-gpt-4.1' }, 'gpt-4.1-2025-04-14', 'openai');
      expect(response.model).toBe('gpt-4.1-2025-04-14');

      // Case 2: Client model name includes the sw- model name (without prefix)
      response = formatResponse({ ...accumulatedData, model: 'sw-claude-base' }, 'client-claude-base-extended', 'openai');
      expect(response.model).toBe('client-claude-base-extended');
      
      // Case 3: No direct match, use the model from accumulatedData
      response = formatResponse({ ...accumulatedData, model: 'sw-completely-different' }, 'some-other-model', 'openai');
      expect(response.model).toBe('sw-completely-different');
    });
  });

  it('should throw error for unknown endpointType', () => {
    expect(() => formatResponse({} , 'any-model', 'unknown')).toThrow('Cannot format response: Unknown endpoint type.');
    expect(logger.error).toHaveBeenCalledWith('Unknown endpointType for response formatting', { endpointType: 'unknown' });
  });
});