const fs = require('fs');
const path = require('path');
const logger = require('./logger');

// Mock fs and console
jest.mock('fs');
jest.spyOn(console, 'log').mockImplementation(() => {});
jest.spyOn(console, 'error').mockImplementation(() => {});
jest.spyOn(console, 'warn').mockImplementation(() => {});
jest.spyOn(console, 'debug').mockImplementation(() => {});

describe('Logger', () => {
  const originalEnv = process.env;

  beforeEach(() => {
    jest.clearAllMocks();
    // Reset process.env
    process.env = { ...originalEnv };
    process.env.NODE_ENV = 'test'; // Prevent initialization logs
  });

  afterEach(() => {
    process.env = originalEnv;
  });

  describe('Log levels', () => {
    it('should respect DEBUG log level', () => {
      process.env.LOG_LEVEL = 'DEBUG';
      const logger = require('./logger'); // Reload logger with new LOG_LEVEL

      logger.debug('Debug message');
      logger.info('Info message');
      logger.warn('Warn message');
      logger.error('Error message');

      expect(console.debug).toHaveBeenCalled();
      expect(console.log).toHaveBeenCalled();
      expect(console.warn).toHaveBeenCalled();
      expect(console.error).toHaveBeenCalled();
      expect(fs.appendFile).toHaveBeenCalledTimes(4);
    });

    it('should respect INFO log level', () => {
      process.env.LOG_LEVEL = 'INFO';
      const logger = require('./logger');

      logger.debug('Debug message');
      logger.info('Info message');
      logger.warn('Warn message');
      logger.error('Error message');

      expect(console.debug).not.toHaveBeenCalled();
      expect(console.log).toHaveBeenCalled();
      expect(console.warn).toHaveBeenCalled();
      expect(console.error).toHaveBeenCalled();
      expect(fs.appendFile).toHaveBeenCalledTimes(3);
    });

    it('should respect WARN log level', () => {
      process.env.LOG_LEVEL = 'WARN';
      const logger = require('./logger');

      logger.debug('Debug message');
      logger.info('Info message');
      logger.warn('Warn message');
      logger.error('Error message');

      expect(console.debug).not.toHaveBeenCalled();
      expect(console.log).not.toHaveBeenCalled();
      expect(console.warn).toHaveBeenCalled();
      expect(console.error).toHaveBeenCalled();
      expect(fs.appendFile).toHaveBeenCalledTimes(2);
    });

    it('should respect ERROR log level', () => {
      process.env.LOG_LEVEL = 'ERROR';
      const logger = require('./logger');

      logger.debug('Debug message');
      logger.info('Info message');
      logger.warn('Warn message');
      logger.error('Error message');

      expect(console.debug).not.toHaveBeenCalled();
      expect(console.log).not.toHaveBeenCalled();
      expect(console.warn).not.toHaveBeenCalled();
      expect(console.error).toHaveBeenCalled();
      expect(fs.appendFile).toHaveBeenCalledTimes(1);
    });
  });

  describe('Log formatting', () => {
    it('should format log message with timestamp and level', () => {
      const message = 'Test message';
      logger.info(message);

      expect(fs.appendFile).toHaveBeenCalledWith(
        expect.any(String),
        expect.stringMatching(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{3}Z \[INFO\] Test message\n$/),
        expect.any(Function)
      );
    });

    it('should include formatted details in log message', () => {
      const message = 'Test message';
      const details = { testkey: 'value', nested: { prop: 'test' } };
      logger.info(message, details);

      expect(fs.appendFile).toHaveBeenCalledWith(
        expect.any(String),
        expect.stringContaining('"testkey": "value"'),
        expect.any(Function)
      );
      expect(fs.appendFile).toHaveBeenCalledWith(
        expect.any(String),
        expect.stringContaining('"nested": {'),
        expect.any(Function)
      );
    });

    it('should handle circular references in details', () => {
      const message = 'Test message';
      const details = { key: 'value' };
      details.circular = details;
      logger.info(message, details);

      expect(fs.appendFile).toHaveBeenCalledWith(
        expect.any(String),
        expect.stringContaining('Error serializing details'),
        expect.any(Function)
      );
    });
  });

  describe('Sensitive data redaction', () => {
    it('should redact authorization headers', () => {
      const message = 'Test message';
      const details = {
        headers: {
          authorization: 'Bearer secret-token',
          'x-api-key': 'secret-key'
        }
      };
      logger.info(message, details);

      const appendFileCall = fs.appendFile.mock.calls[0][1];
      expect(appendFileCall).toContain('[REDACTED]');
      expect(appendFileCall).not.toContain('secret-token');
      expect(appendFileCall).toContain('[REDACTED_KEY]');
      expect(appendFileCall).not.toContain('secret-key');
    });

    it('should redact sensitive fields', () => {
      const message = 'Test message';
      const details = {
        token: 'secret-token',
        apiKey: 'secret-key',
        license: 'secret-license',
        signature: 'secret-signature',
        'x-id': 'secret-id'
      };
      logger.info(message, details);

      const appendFileCall = fs.appendFile.mock.calls[0][1];
      expect(appendFileCall).toContain('[REDACTED_TOKEN]');
      expect(appendFileCall).toContain('[REDACTED_KEY]');
      expect(appendFileCall).toContain('[REDACTED_LICENSE]');
      expect(appendFileCall).toContain('[REDACTED_SIGNATURE]');
      expect(appendFileCall).toContain('[REDACTED_X-ID]');
      expect(appendFileCall).not.toContain('secret-');
    });

    it('should truncate long string values', () => {
      const message = 'Test message';
      const longString = 'a'.repeat(2000);
      const details = { content: longString };
      logger.info(message, details);

      const appendFileCall = fs.appendFile.mock.calls[0][1];
      expect(appendFileCall).toContain('[TRUNCATED]');
      expect(appendFileCall.length).toBeLessThan(longString.length);
    });
  });

  describe('Error object handling', () => {
    it('should properly format error objects', () => {
      const message = 'Test error';
      const error = new Error('Something went wrong');
      error.code = 'ERR_TEST';
      logger.error(message, error);

      const appendFileCall = fs.appendFile.mock.calls[0][1];
      expect(appendFileCall).toContain('Something went wrong');
      expect(appendFileCall).toContain('stack');
      expect(appendFileCall).toContain('ERR_TEST');
    });

    it('should handle errors without stack traces', () => {
      const message = 'Test error';
      const error = { message: 'Custom error without stack' };
      logger.error(message, error);

      const appendFileCall = fs.appendFile.mock.calls[0][1];
      expect(appendFileCall).toContain('Custom error without stack');
    });
  });

  describe('File writing', () => {
    it('should fall back to console on file write error', () => {
      fs.appendFile.mockImplementationOnce((path, data, callback) => {
        callback(new Error('Failed to write'));
      });

      logger.info('Test message');

      expect(console.error).toHaveBeenCalledWith(
        expect.stringContaining('Failed to write to log file:'),
        expect.any(Object)
      );
      expect(console.log).toHaveBeenCalledWith(
        expect.stringContaining('Test message')
      );
    });
  });
});
