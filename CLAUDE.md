# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

- `npm start` - Start the server in production mode
- `npm run dev` - Start the server in development mode with nodemon
- `npm test` - Run tests (if configured)
- `npm install` - Install dependencies

## Architecture Overview

This is a Node.js Express server that acts as a proxy/gateway for multiple LLM APIs, providing a unified interface for chat completions and other AI services.

### Core Structure

- **Entry Point**: `src/server.js` - Main Express server setup with middleware and route mounting
- **Routes**: `src/routes/` - API endpoint handlers
  - `chatCompletionsRouter.js` - Main chat completions endpoint with provider routing
  - `modelsRouter.js` - Available models endpoint
- **Services**: `src/services/` - External API integrations and business logic
- **Config**: `src/config/` - Configuration files including model definitions
- **Utils**: `src/utils/` - Shared utilities and helpers

### Key Components

**Chat Completions Flow**:
1. Requests come to `/v1/chat/completions` endpoint
2. `chatCompletionsRouter.js` handles routing based on model selection
3. Different providers (OpenAI, Anthropic, Google, etc.) are supported
4. Responses are normalized to OpenAI-compatible format

**Model Configuration**:
- Models are defined in `src/config/models.js`
- Each model specifies its provider, capabilities, and routing information
- The `/v1/models` endpoint exposes available models

**Provider Integration**:
- Multiple LLM providers supported (OpenAI, Anthropic, Google, Groq, etc.)
- Each provider has its own service implementation
- Unified response format across all providers

### Environment Configuration

The server requires various API keys and configuration through environment variables for different LLM providers. Check the existing code for specific environment variable names needed for each provider.

### Request Flow

1. Client sends OpenAI-compatible request to `/v1/chat/completions`
2. Server identifies the model and routes to appropriate provider
3. Provider-specific service handles the API call
4. Response is normalized and returned to client

This architecture allows the server to act as a universal LLM gateway, supporting multiple providers through a single, consistent API interface.
